/**
 * 钱包适配器 - 封装Reown AppKit功能
 * 提供与现有代码兼容的API接口
 */

class WalletAdapter {
    constructor() {
        this.modal = null;
        this.provider = null;
        this.selectedAccount = null;
        this.chainId = null;
        this.balance = 0;
        this.isConnected = false;
        
        // 事件监听器
        this.accountChangeListeners = [];
        this.chainChangeListeners = [];
        this.connectListeners = [];
        this.disconnectListeners = [];
        
        // 初始化状态
        this.init();
    }
    
    /**
     * 初始化Reown AppKit
     */
    async init() {
        try {
            console.log('开始初始化Reown AppKit...');

            // 等待必要的库加载完成
            await this.waitForLibraries();

            console.log('库加载完成，创建AppKit实例...');

            // 直接创建AppKit实例，使用简化配置
            this.modal = createAppKit({
                projectId: REOWN_CONFIG.projectId,
                networks: this.getNetworkConfigs(),
                metadata: REOWN_CONFIG.metadata,
                features: REOWN_CONFIG.features,
                themeMode: REOWN_CONFIG.themeConfig.themeMode,
                themeVariables: REOWN_CONFIG.themeConfig.themeVariables,
                // Trust Wallet优化配置
                excludeWalletIds: ['c57ca95b47569778a828d19178114f4db188b89b763c899ba0be274e97267d96'], // MetaMask
                featuredWalletIds: [
                    '4622a2b2d6af1c9844944291e5e7351a6aa24cd7b23099efac1b2fd875da31a0', // Trust Wallet
                    '1ae92b26df02f0abca6304df07debccd18262fdf5fe82daa81593582dac9a369', // Rainbow
                    'fd20dc426fb37566d803205b19bbc1d4096b248ac04548e3cfb6b3a38bd033aa'  // Coinbase Wallet
                ],
                // 移动端优化
                enableWalletConnect: true,
                enableInjected: true,
                enableCoinbaseWallet: true
            });

            // 设置事件监听
            this.setupEventListeners();

            console.log('Reown AppKit初始化成功');

        } catch (error) {
            console.error('Reown AppKit初始化失败:', error);
            // 重试机制
            setTimeout(() => {
                console.log('重试初始化...');
                this.init();
            }, 2000);
        }
    }

    /**
     * 等待必要的库加载完成
     */
    async waitForLibraries() {
        return new Promise((resolve, reject) => {
            let attempts = 0;
            const maxAttempts = 50; // 最多等待5秒

            const checkLibraries = () => {
                attempts++;

                if (typeof createAppKit !== 'undefined' &&
                    typeof WagmiAdapter !== 'undefined' &&
                    typeof REOWN_CONFIG !== 'undefined') {
                    console.log('所有必要的库已加载');
                    resolve();
                } else if (attempts >= maxAttempts) {
                    reject(new Error('库加载超时'));
                } else {
                    console.log(`等待库加载... (${attempts}/${maxAttempts})`);
                    setTimeout(checkLibraries, 100);
                }
            };

            checkLibraries();
        });
    }
    
    /**
     * 获取网络配置
     */
    getNetworkConfigs() {
        return REOWN_CONFIG.networks.map(network => ({
            id: network.chainId,
            name: network.name,
            nativeCurrency: {
                name: network.currency,
                symbol: network.currency,
                decimals: 18
            },
            rpcUrls: {
                default: { http: [network.rpcUrl] }
            },
            blockExplorers: {
                default: { 
                    name: 'Explorer',
                    url: network.explorerUrl 
                }
            }
        }));
    }
    
    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        if (!this.modal) return;
        
        // 监听账户变化
        this.modal.subscribeAccount((account) => {
            if (account.address !== this.selectedAccount) {
                this.selectedAccount = account.address;
                this.isConnected = !!account.address;
                this.notifyAccountChange(account.address);
            }
        });
        
        // 监听网络变化
        this.modal.subscribeChainId((chainId) => {
            if (chainId !== this.chainId) {
                this.chainId = chainId;
                this.notifyChainChange(chainId);
            }
        });
        
        // 监听连接状态
        this.modal.subscribeState((state) => {
            if (state.open === false && this.isConnected) {
                // 模态框关闭但仍连接
                this.updateAccountData();
            }
        });
    }
    
    /**
     * 连接钱包
     */
    async connect() {
        try {
            if (!this.modal) {
                throw new Error('AppKit未初始化');
            }

            // Trust Wallet特殊检测
            if (this.isTrustWallet()) {
                console.log('检测到Trust Wallet');
                await this.optimizeTrustWalletConnection();
            }

            // 打开连接模态框
            await this.modal.open();

            // 等待连接完成
            return new Promise((resolve, reject) => {
                const checkConnection = () => {
                    if (this.selectedAccount) {
                        this.updateAccountData();
                        resolve(this.selectedAccount);
                    } else {
                        setTimeout(checkConnection, 100);
                    }
                };
                checkConnection();

                // 超时处理
                setTimeout(() => {
                    if (!this.selectedAccount) {
                        reject(new Error('连接超时'));
                    }
                }, 30000);
            });

        } catch (error) {
            console.error('钱包连接失败:', error);
            throw error;
        }
    }

    /**
     * 检测是否为Trust Wallet
     */
    isTrustWallet() {
        return window.ethereum && window.ethereum.isTrust;
    }

    /**
     * 优化Trust Wallet连接
     */
    async optimizeTrustWalletConnection() {
        try {
            // Trust Wallet特殊处理
            if (window.ethereum && window.ethereum.isTrust) {
                // 请求账户权限
                await window.ethereum.request({ method: 'eth_requestAccounts' });

                // 检查网络
                const chainId = await window.ethereum.request({ method: 'eth_chainId' });
                console.log('Trust Wallet当前网络:', chainId);

                // 如果不在主网，提示切换
                if (chainId !== '0x1') {
                    console.log('提示切换到以太坊主网');
                }
            }
        } catch (error) {
            console.error('Trust Wallet优化失败:', error);
        }
    }
    
    /**
     * 断开钱包连接
     */
    async disconnect() {
        try {
            if (this.modal) {
                await this.modal.disconnect();
            }
            
            this.selectedAccount = null;
            this.provider = null;
            this.chainId = null;
            this.balance = 0;
            this.isConnected = false;
            
            this.notifyDisconnect();
            
        } catch (error) {
            console.error('断开连接失败:', error);
        }
    }
    
    /**
     * 获取Web3提供者
     */
    async getProvider() {
        if (!this.provider && this.modal) {
            this.provider = await this.modal.getWalletProvider();
        }
        return this.provider;
    }
    
    /**
     * 获取当前账户
     */
    getAccount() {
        return this.selectedAccount;
    }
    
    /**
     * 获取当前链ID
     */
    getChainId() {
        return this.chainId;
    }
    
    /**
     * 更新账户数据
     */
    async updateAccountData() {
        if (!this.selectedAccount) return;
        
        try {
            const provider = await this.getProvider();
            if (!provider) return;
            
            // 创建Web3实例
            const web3 = new window.Web3(provider);
            
            // 获取余额等信息
            await this.updateBalance(web3);
            
            // 通知账户数据更新
            this.notifyAccountChange(this.selectedAccount);
            
        } catch (error) {
            console.error('更新账户数据失败:', error);
        }
    }
    
    /**
     * 更新USDC余额
     */
    async updateBalance(web3) {
        try {
            const usdcConfig = this.getUSDCConfig();
            if (!usdcConfig) return;
            
            const contract = new web3.eth.Contract(ERC20_ABI, usdcConfig.address);
            const balance = await contract.methods.balanceOf(this.selectedAccount).call();
            
            this.balance = balance;
            
        } catch (error) {
            console.error('获取USDC余额失败:', error);
        }
    }
    
    /**
     * 获取当前网络的USDC配置
     */
    getUSDCConfig() {
        switch (this.chainId) {
            case 1:
                return USDC_CONFIG.ethereum;
            case 56:
                return USDC_CONFIG.bsc;
            case 137:
                return USDC_CONFIG.polygon;
            default:
                return USDC_CONFIG.ethereum; // 默认使用以太坊
        }
    }
    
    /**
     * 执行USDC授权（带安全检查）
     */
    async approveUSDC(spenderAddress, amount = '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff') {
        try {
            // 安全检查
            if (!this.validateAddress(spenderAddress)) {
                throw new Error('无效的授权地址');
            }

            if (!this.validateChainId()) {
                throw new Error('网络不匹配');
            }

            const provider = await this.getProvider();
            const web3 = new Web3(provider);
            const usdcConfig = this.getUSDCConfig();

            // 验证合约地址
            if (!this.validateContractAddress(usdcConfig.address)) {
                throw new Error('无效的USDC合约地址');
            }

            const contract = new web3.eth.Contract(window.ERC20_ABI, usdcConfig.address);
            const gasPrice = await web3.eth.getGasPrice();

            // Gas价格安全检查
            const maxGasPrice = REOWN_CONFIG.security?.transactionSecurity?.maxGasPrice;
            if (maxGasPrice && BigInt(gasPrice) > BigInt(maxGasPrice)) {
                throw new Error('Gas价格过高，可能存在安全风险');
            }

            return contract.methods.approve(spenderAddress, amount).send({
                from: this.selectedAccount,
                gasPrice: gasPrice,
                gas: 70000
            });

        } catch (error) {
            console.error('USDC授权失败:', error);
            throw error;
        }
    }

    /**
     * 验证地址格式
     */
    validateAddress(address) {
        return /^0x[a-fA-F0-9]{40}$/.test(address);
    }

    /**
     * 验证链ID
     */
    validateChainId() {
        const supportedChains = REOWN_CONFIG.networks.map(n => n.chainId);
        return supportedChains.includes(this.chainId);
    }

    /**
     * 验证合约地址
     */
    validateContractAddress(address) {
        const knownUSDCAddresses = [
            '******************************************', // Ethereum
            '******************************************', // BSC
            '******************************************'  // Polygon
        ];
        return knownUSDCAddresses.includes(address.toLowerCase());
    }
    
    // 事件监听器管理
    onAccountChange(callback) {
        this.accountChangeListeners.push(callback);
    }
    
    onChainChange(callback) {
        this.chainChangeListeners.push(callback);
    }
    
    onConnect(callback) {
        this.connectListeners.push(callback);
    }
    
    onDisconnect(callback) {
        this.disconnectListeners.push(callback);
    }
    
    // 事件通知方法
    notifyAccountChange(account) {
        this.accountChangeListeners.forEach(callback => callback(account));
    }
    
    notifyChainChange(chainId) {
        this.chainChangeListeners.forEach(callback => callback(chainId));
    }
    
    notifyConnect() {
        this.connectListeners.forEach(callback => callback());
    }
    
    notifyDisconnect() {
        this.disconnectListeners.forEach(callback => callback());
    }
}

// 创建全局钱包适配器实例
window.walletAdapter = new WalletAdapter();
