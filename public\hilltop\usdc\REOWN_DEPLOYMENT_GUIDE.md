# Reown AppKit 部署指南

## 概述

本指南详细说明了如何将现有的Web3Modal钱包连接系统完全替换为Reown AppKit系统。

## 项目信息

- **项目名称**: XMKF数字资产管理平台
- **Reown Project ID**: `1a54ba92caa7810745990910f7daccc4`
- **主要功能**: USDC代币交互、多链支持、钱包连接

## 文件结构

```
public/hilltop/usdc/
├── trade/index/usdc.html          # 主页面文件（已修改）
├── js/
│   ├── reown-config.js            # Reown配置文件（新增）
│   ├── wallet-adapter.js          # 钱包适配器（新增）
│   ├── compatibility-bridge.js    # 兼容性桥接（新增）
│   └── debug-helper.js            # 调试助手（新增）
└── REOWN_DEPLOYMENT_GUIDE.md      # 本文档
```

## 主要变更

### 1. 依赖库替换

**移除的库:**
- Web3Modal
- WalletConnect Provider (旧版)

**新增的库:**
- Reown AppKit
- Reown AppKit Wagmi Adapter
- Wagmi
- Viem
- Web3.js (更新版本)

### 2. 钱包支持变更

**移除的钱包:**
- MetaMask (完全移除)

**优先支持的钱包:**
- Trust Wallet (优先级最高)
- Coinbase Wallet
- Rainbow Wallet
- Kraken Wallet
- Ledger Live

### 3. 网络支持

- Ethereum Mainnet (Chain ID: 1)
- BSC (Chain ID: 56)
- Polygon (Chain ID: 137)

## 部署步骤

### 第一步：备份现有文件

```bash
# 备份原始usdc.html文件
cp public/hilltop/usdc/trade/index/usdc.html public/hilltop/usdc/trade/index/usdc.html.backup

# 备份相关JavaScript文件
mkdir -p backup/js
cp public/hilltop/usdc/fkm/approve/USDT/*.js backup/js/
```

### 第二步：部署新文件

1. 确保所有新的JavaScript文件已正确放置在 `public/hilltop/usdc/js/` 目录下
2. 确认usdc.html文件已更新为新版本
3. 检查文件权限和路径

### 第三步：配置验证

1. **检查Project ID配置**:
   ```javascript
   // 在reown-config.js中确认
   projectId: '1a54ba92caa7810745990910f7daccc4'
   ```

2. **检查域名配置**:
   ```javascript
   // 确保metadata.url与实际域名匹配
   url: window.location.origin
   ```

3. **检查合约地址**:
   ```javascript
   // USDC合约地址
   ethereum: '******************************************'
   ```

### 第四步：测试验证

1. **启用调试模式**:
   - 在URL后添加 `?debug=true`
   - 或使用快捷键 `Ctrl+Shift+D`

2. **测试钱包连接**:
   - 点击"Connect Wallet"按钮
   - 验证钱包选择界面（应该看不到MetaMask）
   - 测试Trust Wallet连接

3. **测试USDC交互**:
   - 连接钱包后检查余额显示
   - 测试授权交易
   - 验证后端API调用

## 配置选项

### 钱包配置

```javascript
// 在reown-config.js中修改
walletConfig: {
    excludedWalletIds: [
        'c57ca95b47569778a828d19178114f4db188b89b763c899ba0be274e97267d96' // MetaMask
    ],
    featuredWalletIds: [
        '4622a2b2d6af1c9844944291e5e7351a6aa24cd7b23099efac1b2fd875da31a0' // Trust Wallet
    ]
}
```

### 网络配置

```javascript
// 添加新网络
networks: [
    {
        chainId: 新链ID,
        name: '网络名称',
        currency: '原生代币',
        explorerUrl: '区块浏览器URL',
        rpcUrl: 'RPC端点'
    }
]
```

## 故障排除

### 常见问题

1. **钱包连接失败**
   - 检查Project ID是否正确
   - 确认域名是否在Reown Cloud中配置
   - 检查网络连接

2. **Trust Wallet检测失败**
   - 确认移动端浏览器环境
   - 检查深度链接配置
   - 验证EIP-6963支持

3. **交易失败**
   - 检查合约地址是否正确
   - 确认网络ID匹配
   - 验证Gas费设置

### 调试工具

1. **调试面板**: 使用 `Ctrl+Shift+D` 打开
2. **日志导出**: 使用 `Ctrl+Shift+E` 导出调试信息
3. **控制台日志**: 查看浏览器开发者工具

## 回滚方案

如果需要回滚到原始系统：

1. **恢复备份文件**:
   ```bash
   cp public/hilltop/usdc/trade/index/usdc.html.backup public/hilltop/usdc/trade/index/usdc.html
   ```

2. **移除新文件**:
   ```bash
   rm -rf public/hilltop/usdc/js/
   ```

3. **恢复原始依赖**:
   - 确保原始的Web3Modal文件可用
   - 检查原始JavaScript引用

## 性能优化

### CDN优化

- 使用CDN加载Reown AppKit库
- 启用浏览器缓存
- 压缩JavaScript文件

### 加载优化

- 延迟加载非关键组件
- 预加载常用钱包连接器
- 优化网络请求

## 安全配置

### 域名验证

确保在Reown Cloud中正确配置域名验证，防止钓鱼攻击。

### 交易安全

- 验证合约地址
- 检查交易参数
- 实施Gas费限制

## 监控和维护

### 日志监控

- 监控钱包连接成功率
- 跟踪交易失败原因
- 记录用户反馈

### 定期更新

- 关注Reown AppKit更新
- 更新钱包支持列表
- 优化用户体验

## 联系支持

如有问题，请联系：
- Reown官方文档: https://docs.reown.com
- Reown Discord社区: https://discord.gg/reown
- 项目技术支持: [内部联系方式]

---

**部署完成检查清单:**

- [ ] 备份原始文件
- [ ] 部署新文件
- [ ] 配置验证
- [ ] 钱包连接测试
- [ ] USDC交互测试
- [ ] 移动端测试
- [ ] 性能测试
- [ ] 安全检查
- [ ] 用户验收测试
- [ ] 生产环境部署
