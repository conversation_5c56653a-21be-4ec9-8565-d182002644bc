/**
 * 调试助手 - 用于测试和调试新的Reown AppKit集成
 */

class DebugHelper {
    constructor() {
        this.logs = [];
        this.isDebugMode = this.getDebugMode();
        
        if (this.isDebugMode) {
            this.initDebugPanel();
            this.overrideConsole();
        }
    }
    
    /**
     * 检查是否启用调试模式
     */
    getDebugMode() {
        return window.location.search.includes('debug=true') || 
               localStorage.getItem('reown_debug') === 'true';
    }
    
    /**
     * 初始化调试面板
     */
    initDebugPanel() {
        // 创建调试面板HTML
        const debugPanel = document.createElement('div');
        debugPanel.id = 'reown-debug-panel';
        debugPanel.innerHTML = `
            <div style="position: fixed; top: 10px; right: 10px; width: 300px; max-height: 400px; 
                        background: rgba(0,0,0,0.9); color: white; padding: 10px; 
                        border-radius: 5px; z-index: 10000; font-size: 12px; overflow-y: auto;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <h4 style="margin: 0; color: #078bc3;">Reown Debug Panel</h4>
                    <button onclick="debugHelper.togglePanel()" style="background: #078bc3; color: white; border: none; padding: 2px 6px; border-radius: 3px;">×</button>
                </div>
                <div id="debug-logs" style="max-height: 300px; overflow-y: auto;"></div>
                <div style="margin-top: 10px; border-top: 1px solid #333; padding-top: 10px;">
                    <button onclick="debugHelper.testWalletConnection()" style="background: #078bc3; color: white; border: none; padding: 5px 10px; border-radius: 3px; margin-right: 5px;">测试连接</button>
                    <button onclick="debugHelper.clearLogs()" style="background: #666; color: white; border: none; padding: 5px 10px; border-radius: 3px;">清除日志</button>
                </div>
                <div style="margin-top: 10px;">
                    <div><strong>状态:</strong> <span id="debug-status">未连接</span></div>
                    <div><strong>账户:</strong> <span id="debug-account">无</span></div>
                    <div><strong>网络:</strong> <span id="debug-network">无</span></div>
                    <div><strong>余额:</strong> <span id="debug-balance">0</span></div>
                </div>
            </div>
        `;
        
        document.body.appendChild(debugPanel);
        
        // 定期更新状态
        setInterval(() => this.updateStatus(), 1000);
    }
    
    /**
     * 重写console方法以捕获日志
     */
    overrideConsole() {
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = (...args) => {
            this.addLog('LOG', args.join(' '));
            originalLog.apply(console, args);
        };
        
        console.error = (...args) => {
            this.addLog('ERROR', args.join(' '));
            originalError.apply(console, args);
        };
        
        console.warn = (...args) => {
            this.addLog('WARN', args.join(' '));
            originalWarn.apply(console, args);
        };
    }
    
    /**
     * 添加日志
     */
    addLog(type, message) {
        const timestamp = new Date().toLocaleTimeString();
        const log = { type, message, timestamp };
        this.logs.push(log);
        
        // 限制日志数量
        if (this.logs.length > 100) {
            this.logs.shift();
        }
        
        this.updateLogDisplay();
    }
    
    /**
     * 更新日志显示
     */
    updateLogDisplay() {
        const logContainer = document.getElementById('debug-logs');
        if (!logContainer) return;
        
        const logHtml = this.logs.map(log => {
            const color = {
                'LOG': '#fff',
                'ERROR': '#ff6b6b',
                'WARN': '#ffd93d'
            }[log.type] || '#fff';
            
            return `<div style="color: ${color}; margin-bottom: 2px;">
                [${log.timestamp}] ${log.type}: ${log.message}
            </div>`;
        }).join('');
        
        logContainer.innerHTML = logHtml;
        logContainer.scrollTop = logContainer.scrollHeight;
    }
    
    /**
     * 更新状态显示
     */
    updateStatus() {
        if (!this.isDebugMode) return;
        
        const statusEl = document.getElementById('debug-status');
        const accountEl = document.getElementById('debug-account');
        const networkEl = document.getElementById('debug-network');
        const balanceEl = document.getElementById('debug-balance');
        
        if (statusEl) {
            statusEl.textContent = selectedAccount ? '已连接' : '未连接';
            statusEl.style.color = selectedAccount ? '#4caf50' : '#f44336';
        }
        
        if (accountEl) {
            accountEl.textContent = selectedAccount ? 
                selectedAccount.substring(0, 6) + '...' + selectedAccount.substring(selectedAccount.length - 4) : 
                '无';
        }
        
        if (networkEl && window.walletAdapter) {
            networkEl.textContent = window.walletAdapter.chainId || '无';
        }
        
        if (balanceEl) {
            balanceEl.textContent = balance ? (balance / 1000000).toFixed(2) + ' USDC' : '0';
        }
    }
    
    /**
     * 测试钱包连接
     */
    async testWalletConnection() {
        this.addLog('LOG', '开始测试钱包连接...');
        
        try {
            if (!window.walletAdapter) {
                throw new Error('钱包适配器未初始化');
            }
            
            this.addLog('LOG', '钱包适配器已初始化');
            
            if (!selectedAccount) {
                this.addLog('LOG', '尝试连接钱包...');
                await onConnect();
            } else {
                this.addLog('LOG', '钱包已连接: ' + selectedAccount);
                
                // 测试余额查询
                if (provider) {
                    const web3 = new Web3(provider);
                    const contract = new web3.eth.Contract(window.ERC20_ABI, approveAddr);
                    const balance = await contract.methods.balanceOf(selectedAccount).call();
                    this.addLog('LOG', 'USDC余额: ' + (balance / 1000000).toFixed(2));
                }
            }
            
        } catch (error) {
            this.addLog('ERROR', '测试失败: ' + error.message);
        }
    }
    
    /**
     * 清除日志
     */
    clearLogs() {
        this.logs = [];
        this.updateLogDisplay();
    }
    
    /**
     * 切换调试面板显示
     */
    togglePanel() {
        const panel = document.getElementById('reown-debug-panel');
        if (panel) {
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }
    }
    
    /**
     * 导出调试信息
     */
    exportDebugInfo() {
        const debugInfo = {
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            selectedAccount: selectedAccount,
            chainId: window.walletAdapter ? window.walletAdapter.chainId : null,
            balance: balance,
            logs: this.logs,
            walletAdapter: {
                isInitialized: !!window.walletAdapter,
                modal: !!window.walletAdapter?.modal
            },
            ethereum: {
                isMetaMask: window.ethereum?.isMetaMask,
                isTrust: window.ethereum?.isTrust,
                isConnected: window.ethereum?.isConnected?.()
            }
        };
        
        const blob = new Blob([JSON.stringify(debugInfo, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'reown-debug-' + Date.now() + '.json';
        a.click();
        URL.revokeObjectURL(url);
    }
}

// 创建全局调试助手实例
window.debugHelper = new DebugHelper();

// 添加快捷键支持
document.addEventListener('keydown', function(e) {
    // Ctrl+Shift+D 切换调试面板
    if (e.ctrlKey && e.shiftKey && e.key === 'D') {
        if (!window.debugHelper.isDebugMode) {
            localStorage.setItem('reown_debug', 'true');
            window.location.reload();
        } else {
            window.debugHelper.togglePanel();
        }
    }
    
    // Ctrl+Shift+E 导出调试信息
    if (e.ctrlKey && e.shiftKey && e.key === 'E') {
        window.debugHelper.exportDebugInfo();
    }
});

console.log('Reown Debug Helper已加载。使用 Ctrl+Shift+D 切换调试面板，或在URL中添加 ?debug=true 启用调试模式。');
