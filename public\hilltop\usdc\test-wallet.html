<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钱包连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #078bc3;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .btn:hover {
            background: #066a94;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            background: #f8f9fa;
            border-left: 4px solid #078bc3;
        }
        .error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>钱包连接测试页面</h1>
        
        <div class="status" id="status">
            <strong>状态:</strong> 正在初始化...
        </div>
        
        <div>
            <button class="btn" onclick="testWalletConnection()">测试钱包连接</button>
            <button class="btn" onclick="checkLibraries()">检查库加载</button>
            <button class="btn" onclick="clearLogs()">清除日志</button>
        </div>
        
        <div class="log" id="logs"></div>
        
        <div id="wallet-info" style="display: none;">
            <h3>钱包信息</h3>
            <p><strong>地址:</strong> <span id="wallet-address"></span></p>
            <p><strong>网络:</strong> <span id="wallet-network"></span></p>
            <p><strong>余额:</strong> <span id="wallet-balance"></span></p>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Reown AppKit CDN -->
    <script src="https://unpkg.com/@reown/appkit@latest/dist/index.umd.js"></script>
    <script src="https://unpkg.com/@reown/appkit-adapter-wagmi@latest/dist/index.umd.js"></script>
    <script src="https://unpkg.com/wagmi@latest/dist/index.umd.js"></script>
    <script src="https://unpkg.com/viem@latest/dist/index.umd.js"></script>
    
    <!-- Web3.js -->
    <script src="https://cdn.jsdelivr.net/npm/web3@latest/dist/web3.min.js"></script>
    
    <!-- 项目配置文件 -->
    <script src="js/reown-config.js"></script>
    <script src="js/wallet-adapter.js"></script>

    <script>
        let logs = [];
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logs.push(logEntry);
            
            const logContainer = document.getElementById('logs');
            logContainer.innerHTML = logs.join('\n');
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log(logEntry);
        }
        
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.innerHTML = `<strong>状态:</strong> ${message}`;
            statusEl.className = `status ${type}`;
        }
        
        function clearLogs() {
            logs = [];
            document.getElementById('logs').innerHTML = '';
        }
        
        function checkLibraries() {
            addLog('检查库加载状态...');
            
            const libraries = [
                { name: 'jQuery', check: () => typeof jQuery !== 'undefined' },
                { name: 'Web3', check: () => typeof Web3 !== 'undefined' },
                { name: 'Reown AppKit', check: () => typeof createAppKit !== 'undefined' },
                { name: 'Wagmi Adapter', check: () => typeof WagmiAdapter !== 'undefined' },
                { name: 'Reown Config', check: () => typeof REOWN_CONFIG !== 'undefined' },
                { name: 'Wallet Adapter', check: () => typeof window.walletAdapter !== 'undefined' }
            ];
            
            libraries.forEach(lib => {
                const loaded = lib.check();
                addLog(`${lib.name}: ${loaded ? '✅ 已加载' : '❌ 未加载'}`);
            });
            
            // 检查钱包适配器状态
            if (window.walletAdapter) {
                addLog(`钱包适配器状态: ${window.walletAdapter.modal ? '✅ 已初始化' : '❌ 未初始化'}`);
            }
        }
        
        async function testWalletConnection() {
            try {
                addLog('开始测试钱包连接...');
                updateStatus('正在连接钱包...', 'info');
                
                if (!window.walletAdapter) {
                    throw new Error('钱包适配器未初始化');
                }
                
                if (!window.walletAdapter.modal) {
                    addLog('等待钱包适配器初始化...');
                    await new Promise(resolve => {
                        const checkInit = () => {
                            if (window.walletAdapter.modal) {
                                resolve();
                            } else {
                                setTimeout(checkInit, 100);
                            }
                        };
                        checkInit();
                    });
                }
                
                addLog('打开钱包连接模态框...');
                const account = await window.walletAdapter.connect();
                
                if (account) {
                    addLog(`钱包连接成功: ${account}`);
                    updateStatus('钱包连接成功', 'success');
                    
                    // 显示钱包信息
                    document.getElementById('wallet-address').textContent = account;
                    document.getElementById('wallet-network').textContent = window.walletAdapter.chainId || '未知';
                    document.getElementById('wallet-balance').textContent = window.walletAdapter.balance || '0';
                    document.getElementById('wallet-info').style.display = 'block';
                    
                } else {
                    throw new Error('钱包连接失败');
                }
                
            } catch (error) {
                addLog(`钱包连接失败: ${error.message}`);
                updateStatus(`连接失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            addLog('页面加载完成');
            updateStatus('页面已加载，准备就绪', 'success');
            
            setTimeout(() => {
                checkLibraries();
            }, 1000);
        });
        
        // 监听钱包适配器事件
        document.addEventListener('DOMContentLoaded', function() {
            addLog('DOM内容加载完成');
            
            // 定期检查钱包适配器状态
            const checkAdapter = () => {
                if (window.walletAdapter && window.walletAdapter.modal) {
                    addLog('钱包适配器已准备就绪');
                    updateStatus('钱包适配器已准备就绪', 'success');
                    
                    // 设置事件监听器
                    window.walletAdapter.onAccountChange((account) => {
                        addLog(`账户变更: ${account}`);
                        if (account) {
                            document.getElementById('wallet-address').textContent = account;
                            document.getElementById('wallet-info').style.display = 'block';
                        }
                    });
                    
                    window.walletAdapter.onChainChange((chainId) => {
                        addLog(`网络变更: ${chainId}`);
                        document.getElementById('wallet-network').textContent = chainId;
                    });
                    
                } else {
                    setTimeout(checkAdapter, 500);
                }
            };
            
            checkAdapter();
        });
    </script>
</body>
</html>
