<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>问题调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background: #078bc3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #066a94;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            background: #f8f9fa;
            border-left: 4px solid #078bc3;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .slider-test {
            height: 200px;
            overflow: hidden;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .slider-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
        }
    </style>
    
    <!-- 引入必要的库 -->
    <link rel="stylesheet" href="../../wen/slick.min.css">
    <link rel="stylesheet" href="../../wen/slick-theme.min.css">
</head>
<body>
    <div class="container">
        <h1>🔧 问题调试测试</h1>
        
        <h2>问题1: 钱包连接测试</h2>
        <div class="status" id="wallet-status">检查中...</div>
        <button class="btn" onclick="testWalletConnection()">测试钱包连接</button>
        <button class="btn" onclick="checkWalletEnvironment()">检查钱包环境</button>
        <div class="log" id="wallet-log"></div>
    </div>
    
    <div class="container">
        <h2>问题2: 滚动功能测试</h2>
        <div class="status" id="slider-status">检查中...</div>
        <button class="btn" onclick="testSlider()">测试滚动</button>
        <button class="btn" onclick="checkSliderEnvironment()">检查滚动环境</button>
        
        <div class="slider-test">
            <ul class="slider-for" style="list-style: none; padding: 0; margin: 0;">
                <li class="slider-item"><span>项目 1</span><span>0.001 ETH</span></li>
                <li class="slider-item"><span>项目 2</span><span>0.002 ETH</span></li>
                <li class="slider-item"><span>项目 3</span><span>0.003 ETH</span></li>
                <li class="slider-item"><span>项目 4</span><span>0.004 ETH</span></li>
                <li class="slider-item"><span>项目 5</span><span>0.005 ETH</span></li>
                <li class="slider-item"><span>项目 6</span><span>0.006 ETH</span></li>
                <li class="slider-item"><span>项目 7</span><span>0.007 ETH</span></li>
                <li class="slider-item"><span>项目 8</span><span>0.008 ETH</span></li>
                <li class="slider-item"><span>项目 9</span><span>0.009 ETH</span></li>
                <li class="slider-item"><span>项目 10</span><span>0.010 ETH</span></li>
            </ul>
        </div>
        
        <div class="log" id="slider-log"></div>
    </div>

    <!-- 引入jQuery和slick -->
    <script src="../../wen/jquery-3.6.0.js"></script>
    <script src="../../wen/slick.min.js"></script>

    <script>
        let walletLogs = [];
        let sliderLogs = [];
        
        function addWalletLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            walletLogs.push(`[${timestamp}] ${message}`);
            document.getElementById('wallet-log').textContent = walletLogs.join('\n');
            console.log(`[钱包] ${message}`);
        }
        
        function addSliderLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            sliderLogs.push(`[${timestamp}] ${message}`);
            document.getElementById('slider-log').textContent = sliderLogs.join('\n');
            console.log(`[滚动] ${message}`);
        }
        
        function updateWalletStatus(message, type = 'status') {
            const el = document.getElementById('wallet-status');
            el.textContent = message;
            el.className = `status ${type}`;
        }
        
        function updateSliderStatus(message, type = 'status') {
            const el = document.getElementById('slider-status');
            el.textContent = message;
            el.className = `status ${type}`;
        }
        
        // 钱包连接测试
        async function testWalletConnection() {
            addWalletLog('开始测试钱包连接...');
            updateWalletStatus('测试中...', 'status');
            
            try {
                if (!window.ethereum) {
                    throw new Error('未检测到钱包');
                }
                
                addWalletLog('检测到钱包，请求连接...');
                
                const accounts = await window.ethereum.request({
                    method: 'eth_requestAccounts'
                });
                
                if (accounts.length > 0) {
                    addWalletLog(`连接成功: ${accounts[0]}`);
                    updateWalletStatus('钱包连接成功！', 'success');
                } else {
                    throw new Error('未获取到账户');
                }
                
            } catch (error) {
                addWalletLog(`连接失败: ${error.message}`);
                updateWalletStatus(`连接失败: ${error.message}`, 'error');
            }
        }
        
        function checkWalletEnvironment() {
            addWalletLog('检查钱包环境...');
            
            const checks = [
                { name: 'window.ethereum', check: () => typeof window.ethereum !== 'undefined' },
                { name: 'ethereum.isMetaMask', check: () => window.ethereum?.isMetaMask },
                { name: 'ethereum.isTrust', check: () => window.ethereum?.isTrust },
                { name: 'ethereum.request', check: () => typeof window.ethereum?.request === 'function' }
            ];
            
            checks.forEach(item => {
                const result = item.check();
                addWalletLog(`${item.name}: ${result ? '✅' : '❌'}`);
            });
        }
        
        // 滚动功能测试
        function testSlider() {
            addSliderLog('开始测试滚动功能...');
            updateSliderStatus('测试中...', 'status');
            
            try {
                if (typeof jQuery === 'undefined') {
                    throw new Error('jQuery未加载');
                }
                
                if (typeof jQuery.fn.slick === 'undefined') {
                    throw new Error('Slick插件未加载');
                }
                
                const sliderElement = jQuery('.slider-for');
                if (sliderElement.length === 0) {
                    throw new Error('滚动元素未找到');
                }
                
                addSliderLog('初始化滚动...');
                
                // 如果已经初始化过，先销毁
                if (sliderElement.hasClass('slick-initialized')) {
                    sliderElement.slick('unslick');
                    addSliderLog('销毁旧的滚动实例');
                }
                
                // 初始化滚动
                sliderElement.slick({
                    autoplay: true,
                    arrows: false,
                    dots: false,
                    slidesToShow: 5,
                    slidesToScroll: 1,
                    vertical: true,
                    speed: 600,
                    autoplaySpeed: 1000,
                    infinite: true,
                    pauseOnHover: true
                });
                
                addSliderLog('滚动初始化成功');
                updateSliderStatus('滚动功能正常！', 'success');
                
            } catch (error) {
                addSliderLog(`滚动初始化失败: ${error.message}`);
                updateSliderStatus(`滚动失败: ${error.message}`, 'error');
            }
        }
        
        function checkSliderEnvironment() {
            addSliderLog('检查滚动环境...');
            
            const checks = [
                { name: 'jQuery', check: () => typeof jQuery !== 'undefined' },
                { name: 'jQuery.fn.slick', check: () => typeof jQuery?.fn?.slick !== 'undefined' },
                { name: '.slider-for元素', check: () => document.querySelector('.slider-for') !== null },
                { name: 'slick CSS', check: () => {
                    const links = document.querySelectorAll('link[href*="slick"]');
                    return links.length > 0;
                }}
            ];
            
            checks.forEach(item => {
                const result = item.check();
                addSliderLog(`${item.name}: ${result ? '✅' : '❌'}`);
            });
        }
        
        // 页面加载完成后自动检查
        jQuery(document).ready(function() {
            addWalletLog('页面加载完成');
            addSliderLog('页面加载完成');
            
            setTimeout(() => {
                checkWalletEnvironment();
                checkSliderEnvironment();
            }, 500);
        });
    </script>
</body>
</html>
