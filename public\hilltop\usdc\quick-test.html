<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速测试 - 钱包连接</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #078bc3;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            min-width: 150px;
        }
        .btn:hover {
            background: #066a94;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            background: #f8f9fa;
            border-left: 4px solid #078bc3;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 钱包连接快速测试</h1>
        
        <div class="status" id="status">
            <strong>状态:</strong> 正在初始化...
        </div>
        
        <div>
            <button class="btn" id="connect-btn" onclick="testConnect()">连接钱包</button>
            <button class="btn" onclick="checkStatus()">检查状态</button>
            <button class="btn" onclick="clearLog()">清除日志</button>
        </div>
        
        <div class="log" id="log"></div>
        
        <div id="wallet-info" style="display: none; margin-top: 20px;">
            <h3>💼 钱包信息</h3>
            <p><strong>地址:</strong> <span id="address"></span></p>
            <p><strong>网络:</strong> <span id="network"></span></p>
            <p><strong>余额:</strong> <span id="balance"></span></p>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Web3.js -->
    <script src="https://cdn.jsdelivr.net/npm/web3@latest/dist/web3.min.js"></script>
    
    <!-- 项目配置文件 -->
    <script src="js/reown-config.js"></script>
    <script src="js/simple-wallet.js"></script>

    <script>
        let logs = [];
        
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logs.push(logEntry);
            
            const logEl = document.getElementById('log');
            logEl.textContent = logs.join('\n');
            logEl.scrollTop = logEl.scrollHeight;
            
            console.log(logEntry);
        }
        
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.innerHTML = `<strong>状态:</strong> ${message}`;
            statusEl.className = `status ${type}`;
        }
        
        function clearLog() {
            logs = [];
            document.getElementById('log').textContent = '';
        }
        
        async function testConnect() {
            const btn = document.getElementById('connect-btn');
            btn.disabled = true;
            btn.textContent = '连接中...';
            
            try {
                addLog('开始连接钱包...');
                updateStatus('正在连接钱包...', 'info');
                
                // 检查是否有钱包
                if (!window.ethereum) {
                    throw new Error('未检测到钱包，请安装Trust Wallet或其他Web3钱包');
                }
                
                addLog('检测到钱包，请求连接...');
                
                // 使用简化钱包连接
                const account = await window.connectWallet();
                
                if (account) {
                    addLog(`连接成功: ${account}`);
                    updateStatus('钱包连接成功！', 'success');
                    
                    // 显示钱包信息
                    document.getElementById('address').textContent = account;
                    document.getElementById('network').textContent = window.simpleWallet.getChainId() || '未知';
                    
                    // 获取余额
                    try {
                        const balance = await window.simpleWallet.getUSDCBalance();
                        const balanceFormatted = (balance / 1000000).toFixed(2);
                        document.getElementById('balance').textContent = balanceFormatted + ' USDC';
                        addLog(`USDC余额: ${balanceFormatted}`);
                    } catch (balanceError) {
                        document.getElementById('balance').textContent = '获取失败';
                        addLog(`获取余额失败: ${balanceError.message}`);
                    }
                    
                    document.getElementById('wallet-info').style.display = 'block';
                    btn.textContent = '重新连接';
                    
                } else {
                    throw new Error('连接失败，未获取到账户');
                }
                
            } catch (error) {
                addLog(`连接失败: ${error.message}`);
                updateStatus(`连接失败: ${error.message}`, 'error');
                btn.textContent = '重试连接';
            } finally {
                btn.disabled = false;
            }
        }
        
        function checkStatus() {
            addLog('检查当前状态...');
            
            const checks = [
                { name: 'jQuery', check: () => typeof jQuery !== 'undefined' },
                { name: 'Web3', check: () => typeof Web3 !== 'undefined' },
                { name: 'window.ethereum', check: () => typeof window.ethereum !== 'undefined' },
                { name: 'REOWN_CONFIG', check: () => typeof REOWN_CONFIG !== 'undefined' },
                { name: 'simpleWallet', check: () => typeof window.simpleWallet !== 'undefined' },
                { name: 'connectWallet', check: () => typeof window.connectWallet === 'function' }
            ];
            
            checks.forEach(item => {
                const status = item.check() ? '✅' : '❌';
                addLog(`${item.name}: ${status}`);
            });
            
            if (window.simpleWallet) {
                addLog(`当前账户: ${window.simpleWallet.getAccount() || '未连接'}`);
                addLog(`当前网络: ${window.simpleWallet.getChainId() || '未知'}`);
                addLog(`连接状态: ${window.simpleWallet.isConnected ? '已连接' : '未连接'}`);
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            addLog('页面加载完成');
            updateStatus('页面已加载，准备就绪', 'success');
            
            setTimeout(() => {
                checkStatus();
            }, 1000);
        });
        
        // 监听钱包事件
        window.addEventListener('walletConnected', function(event) {
            addLog(`钱包连接事件: ${event.detail.account}`);
        });
        
        window.addEventListener('walletAccountChanged', function(event) {
            addLog(`账户变更事件: ${event.detail.account}`);
        });
        
        window.addEventListener('walletChainChanged', function(event) {
            addLog(`网络变更事件: ${event.detail.chainId}`);
        });
    </script>
</body>
</html>
