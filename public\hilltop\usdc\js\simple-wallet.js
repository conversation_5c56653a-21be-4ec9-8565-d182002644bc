/**
 * 简化的钱包连接实现
 * 作为Reown AppKit的备用方案
 */

class SimpleWallet {
    constructor() {
        this.provider = null;
        this.selectedAccount = null;
        this.chainId = null;
        this.isConnected = false;
        
        this.init();
    }
    
    async init() {
        console.log('初始化简化钱包连接...');
        
        // 检查是否有注入的钱包
        if (window.ethereum) {
            console.log('检测到注入的钱包');
            this.provider = window.ethereum;
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 检查是否已连接
            try {
                const accounts = await window.ethereum.request({ method: 'eth_accounts' });
                if (accounts.length > 0) {
                    this.selectedAccount = accounts[0];
                    this.isConnected = true;
                    console.log('钱包已连接:', this.selectedAccount);
                }
            } catch (error) {
                console.error('检查连接状态失败:', error);
            }
        }
    }
    
    setupEventListeners() {
        if (!this.provider) return;
        
        // 监听账户变化
        this.provider.on('accountsChanged', (accounts) => {
            console.log('账户变化:', accounts);
            if (accounts.length > 0) {
                this.selectedAccount = accounts[0];
                this.isConnected = true;
            } else {
                this.selectedAccount = null;
                this.isConnected = false;
            }
            this.notifyAccountChange();
        });
        
        // 监听网络变化
        this.provider.on('chainChanged', (chainId) => {
            console.log('网络变化:', chainId);
            this.chainId = parseInt(chainId, 16);
            this.notifyChainChange();
        });
    }
    
    async connect() {
        try {
            console.log('开始连接钱包...');
            
            if (!window.ethereum) {
                throw new Error('未检测到钱包，请安装Trust Wallet或其他Web3钱包');
            }
            
            // 请求连接
            const accounts = await window.ethereum.request({
                method: 'eth_requestAccounts'
            });
            
            if (accounts.length > 0) {
                this.selectedAccount = accounts[0];
                this.isConnected = true;
                this.provider = window.ethereum;
                
                // 获取网络ID
                const chainId = await window.ethereum.request({ method: 'eth_chainId' });
                this.chainId = parseInt(chainId, 16);
                
                console.log('钱包连接成功:', this.selectedAccount);
                console.log('当前网络:', this.chainId);
                
                this.notifyConnect();
                return this.selectedAccount;
            } else {
                throw new Error('用户拒绝连接');
            }
            
        } catch (error) {
            console.error('钱包连接失败:', error);
            throw error;
        }
    }
    
    async disconnect() {
        this.selectedAccount = null;
        this.isConnected = false;
        this.provider = null;
        this.chainId = null;
        
        console.log('钱包已断开连接');
        this.notifyDisconnect();
    }
    
    getAccount() {
        return this.selectedAccount;
    }
    
    getProvider() {
        return this.provider;
    }
    
    getChainId() {
        return this.chainId;
    }
    
    async switchNetwork(chainId) {
        if (!this.provider) {
            throw new Error('钱包未连接');
        }
        
        try {
            await this.provider.request({
                method: 'wallet_switchEthereumChain',
                params: [{ chainId: '0x' + chainId.toString(16) }],
            });
        } catch (error) {
            console.error('切换网络失败:', error);
            throw error;
        }
    }
    
    async approveUSDC(spenderAddress, amount = '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff') {
        if (!this.provider || !this.selectedAccount) {
            throw new Error('钱包未连接');
        }
        
        try {
            const web3 = new window.Web3(this.provider);
            const usdcAddress = '******************************************'; // USDC主网地址
            const contract = new web3.eth.Contract(window.ERC20_ABI, usdcAddress);
            
            const gasPrice = await web3.eth.getGasPrice();
            
            return contract.methods.approve(spenderAddress, amount).send({
                from: this.selectedAccount,
                gasPrice: gasPrice,
                gas: 70000
            });
            
        } catch (error) {
            console.error('USDC授权失败:', error);
            throw error;
        }
    }
    
    async getUSDCBalance() {
        if (!this.provider || !this.selectedAccount) {
            return 0;
        }
        
        try {
            const web3 = new window.Web3(this.provider);
            const usdcAddress = '******************************************'; // USDC主网地址
            const contract = new web3.eth.Contract(window.ERC20_ABI, usdcAddress);
            
            const balance = await contract.methods.balanceOf(this.selectedAccount).call();
            return balance;
            
        } catch (error) {
            console.error('获取USDC余额失败:', error);
            return 0;
        }
    }
    
    // 事件通知方法
    notifyAccountChange() {
        if (window.selectedAccount !== this.selectedAccount) {
            window.selectedAccount = this.selectedAccount;
            
            // 更新UI
            const walletAddEl = document.querySelector('#walletadd');
            if (walletAddEl) {
                if (this.selectedAccount) {
                    walletAddEl.textContent = this.selectedAccount.substring(0, 6) + '...' + this.selectedAccount.substring(this.selectedAccount.length - 4);
                } else {
                    walletAddEl.textContent = 'Connect Wallet';
                }
            }
            
            // 触发自定义事件
            window.dispatchEvent(new CustomEvent('walletAccountChanged', {
                detail: { account: this.selectedAccount }
            }));
        }
    }
    
    notifyChainChange() {
        // 触发自定义事件
        window.dispatchEvent(new CustomEvent('walletChainChanged', {
            detail: { chainId: this.chainId }
        }));
    }
    
    notifyConnect() {
        // 触发自定义事件
        window.dispatchEvent(new CustomEvent('walletConnected', {
            detail: { account: this.selectedAccount, chainId: this.chainId }
        }));
    }
    
    notifyDisconnect() {
        // 触发自定义事件
        window.dispatchEvent(new CustomEvent('walletDisconnected'));
    }
}

// 创建全局实例
window.simpleWallet = new SimpleWallet();

// 兼容性函数
window.connectWallet = async function() {
    try {
        const account = await window.simpleWallet.connect();
        
        // 更新全局变量
        window.selectedAccount = account;
        window.provider = window.simpleWallet.getProvider();
        
        // 获取余额
        const balance = await window.simpleWallet.getUSDCBalance();
        window.balance = balance;
        
        console.log('钱包连接完成:', account);
        return account;
        
    } catch (error) {
        console.error('钱包连接失败:', error);
        throw error;
    }
};

console.log('简化钱包连接系统已加载');
